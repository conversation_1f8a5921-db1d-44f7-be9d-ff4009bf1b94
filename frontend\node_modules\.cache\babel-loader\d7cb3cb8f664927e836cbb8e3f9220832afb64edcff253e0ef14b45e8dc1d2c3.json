{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSpeedDialActionUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDialAction', slot);\n}\nconst speedDialActionClasses = generateUtilityClasses('MuiSpeedDialAction', ['fab', 'fabClosed', 'staticTooltip', 'staticTooltipClosed', 'staticTooltipLabel', 'tooltipPlacementLeft', 'tooltipPlacementRight']);\nexport default speedDialActionClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSpeedDialActionUtilityClass", "slot", "speedDialActionClasses"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/material/esm/SpeedDialAction/speedDialActionClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSpeedDialActionUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDialAction', slot);\n}\nconst speedDialActionClasses = generateUtilityClasses('MuiSpeedDialAction', ['fab', 'fabClosed', 'staticTooltip', 'staticTooltipClosed', 'staticTooltipLabel', 'tooltipPlacementLeft', 'tooltipPlacementRight']);\nexport default speedDialActionClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAE;EACnD,OAAOF,oBAAoB,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AACzD;AACA,MAAMC,sBAAsB,GAAGJ,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,uBAAuB,CAAC,CAAC;AAChN,eAAeI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}