{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectMargin } from './containerSelectors';\nimport { isNumber } from '../../util/DataUtils';\nexport var selectBrushSettings = state => state.brush;\nexport var selectBrushDimensions = createSelector([selectBrushSettings, selectChartOffsetInternal, selectMargin], (brushSettings, offset, margin) => ({\n  height: brushSettings.height,\n  x: isNumber(brushSettings.x) ? brushSettings.x : offset.left,\n  y: isNumber(brushSettings.y) ? brushSettings.y : offset.top + offset.height + offset.brushBottom - ((margin === null || margin === void 0 ? void 0 : margin.bottom) || 0),\n  width: isNumber(brushSettings.width) ? brushSettings.width : offset.width\n}));", "map": {"version": 3, "names": ["createSelector", "selectChartOffsetInternal", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "selectBrushSettings", "state", "brush", "selectBrushDimensions", "brushSettings", "offset", "margin", "height", "x", "left", "y", "top", "brushBottom", "bottom", "width"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/recharts/es6/state/selectors/brushSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectMargin } from './containerSelectors';\nimport { isNumber } from '../../util/DataUtils';\nexport var selectBrushSettings = state => state.brush;\nexport var selectBrushDimensions = createSelector([selectBrushSettings, selectChartOffsetInternal, selectMargin], (brushSettings, offset, margin) => ({\n  height: brushSettings.height,\n  x: isNumber(brushSettings.x) ? brushSettings.x : offset.left,\n  y: isNumber(brushSettings.y) ? brushSettings.y : offset.top + offset.height + offset.brushBottom - ((margin === null || margin === void 0 ? void 0 : margin.bottom) || 0),\n  width: isNumber(brushSettings.width) ? brushSettings.width : offset.width\n}));"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAO,IAAIC,mBAAmB,GAAGC,KAAK,IAAIA,KAAK,CAACC,KAAK;AACrD,OAAO,IAAIC,qBAAqB,GAAGP,cAAc,CAAC,CAACI,mBAAmB,EAAEH,yBAAyB,EAAEC,YAAY,CAAC,EAAE,CAACM,aAAa,EAAEC,MAAM,EAAEC,MAAM,MAAM;EACpJC,MAAM,EAAEH,aAAa,CAACG,MAAM;EAC5BC,CAAC,EAAET,QAAQ,CAACK,aAAa,CAACI,CAAC,CAAC,GAAGJ,aAAa,CAACI,CAAC,GAAGH,MAAM,CAACI,IAAI;EAC5DC,CAAC,EAAEX,QAAQ,CAACK,aAAa,CAACM,CAAC,CAAC,GAAGN,aAAa,CAACM,CAAC,GAAGL,MAAM,CAACM,GAAG,GAAGN,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACO,WAAW,IAAI,CAACN,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,MAAM,KAAK,CAAC,CAAC;EACzKC,KAAK,EAAEf,QAAQ,CAACK,aAAa,CAACU,KAAK,CAAC,GAAGV,aAAa,CAACU,KAAK,GAAGT,MAAM,CAACS;AACtE,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}