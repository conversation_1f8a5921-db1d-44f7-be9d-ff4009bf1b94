{"ast": null, "code": "import { InternMap } from \"internmap\";\nimport identity from \"./identity.js\";\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\nexport function flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\nexport function flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\nfunction nest(values, map, reduce, keys) {\n  return function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  }(values, 0);\n}", "map": {"version": 3, "names": ["InternMap", "identity", "group", "values", "keys", "nest", "groups", "Array", "from", "flatten", "i", "n", "length", "flatMap", "g", "pop", "map", "key", "value", "flatGroup", "flatRollup", "reduce", "rollups", "rollup", "index", "unique", "indexes", "Error", "regroup", "keyof", "get", "push", "set"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-array/src/group.js"], "sourcesContent": ["import {InternMap} from \"internmap\";\nimport identity from \"./identity.js\";\n\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\n\nexport function flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nexport function flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AACnC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,KAAKA,CAACC,MAAM,EAAE,GAAGC,IAAI,EAAE;EAC7C,OAAOC,IAAI,CAACF,MAAM,EAAEF,QAAQ,EAAEA,QAAQ,EAAEG,IAAI,CAAC;AAC/C;AAEA,OAAO,SAASE,MAAMA,CAACH,MAAM,EAAE,GAAGC,IAAI,EAAE;EACtC,OAAOC,IAAI,CAACF,MAAM,EAAEI,KAAK,CAACC,IAAI,EAAEP,QAAQ,EAAEG,IAAI,CAAC;AACjD;AAEA,SAASK,OAAOA,CAACH,MAAM,EAAEF,IAAI,EAAE;EAC7B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGP,IAAI,CAACQ,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC3CJ,MAAM,GAAGA,MAAM,CAACO,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK,CAAC,GAAGJ,CAAC,EAAEG,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC;EACjF;EACA,OAAOZ,MAAM;AACf;AAEA,OAAO,SAASa,SAASA,CAAChB,MAAM,EAAE,GAAGC,IAAI,EAAE;EACzC,OAAOK,OAAO,CAACH,MAAM,CAACH,MAAM,EAAE,GAAGC,IAAI,CAAC,EAAEA,IAAI,CAAC;AAC/C;AAEA,OAAO,SAASgB,UAAUA,CAACjB,MAAM,EAAEkB,MAAM,EAAE,GAAGjB,IAAI,EAAE;EAClD,OAAOK,OAAO,CAACa,OAAO,CAACnB,MAAM,EAAEkB,MAAM,EAAE,GAAGjB,IAAI,CAAC,EAAEA,IAAI,CAAC;AACxD;AAEA,OAAO,SAASmB,MAAMA,CAACpB,MAAM,EAAEkB,MAAM,EAAE,GAAGjB,IAAI,EAAE;EAC9C,OAAOC,IAAI,CAACF,MAAM,EAAEF,QAAQ,EAAEoB,MAAM,EAAEjB,IAAI,CAAC;AAC7C;AAEA,OAAO,SAASkB,OAAOA,CAACnB,MAAM,EAAEkB,MAAM,EAAE,GAAGjB,IAAI,EAAE;EAC/C,OAAOC,IAAI,CAACF,MAAM,EAAEI,KAAK,CAACC,IAAI,EAAEa,MAAM,EAAEjB,IAAI,CAAC;AAC/C;AAEA,OAAO,SAASoB,KAAKA,CAACrB,MAAM,EAAE,GAAGC,IAAI,EAAE;EACrC,OAAOC,IAAI,CAACF,MAAM,EAAEF,QAAQ,EAAEwB,MAAM,EAAErB,IAAI,CAAC;AAC7C;AAEA,OAAO,SAASsB,OAAOA,CAACvB,MAAM,EAAE,GAAGC,IAAI,EAAE;EACvC,OAAOC,IAAI,CAACF,MAAM,EAAEI,KAAK,CAACC,IAAI,EAAEiB,MAAM,EAAErB,IAAI,CAAC;AAC/C;AAEA,SAASqB,MAAMA,CAACtB,MAAM,EAAE;EACtB,IAAIA,MAAM,CAACS,MAAM,KAAK,CAAC,EAAE,MAAM,IAAIe,KAAK,CAAC,eAAe,CAAC;EACzD,OAAOxB,MAAM,CAAC,CAAC,CAAC;AAClB;AAEA,SAASE,IAAIA,CAACF,MAAM,EAAEa,GAAG,EAAEK,MAAM,EAAEjB,IAAI,EAAE;EACvC,OAAQ,SAASwB,OAAOA,CAACzB,MAAM,EAAEO,CAAC,EAAE;IAClC,IAAIA,CAAC,IAAIN,IAAI,CAACQ,MAAM,EAAE,OAAOS,MAAM,CAAClB,MAAM,CAAC;IAC3C,MAAMG,MAAM,GAAG,IAAIN,SAAS,CAAC,CAAC;IAC9B,MAAM6B,KAAK,GAAGzB,IAAI,CAACM,CAAC,EAAE,CAAC;IACvB,IAAIc,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,MAAMN,KAAK,IAAIf,MAAM,EAAE;MAC1B,MAAMc,GAAG,GAAGY,KAAK,CAACX,KAAK,EAAE,EAAEM,KAAK,EAAErB,MAAM,CAAC;MACzC,MAAMD,KAAK,GAAGI,MAAM,CAACwB,GAAG,CAACb,GAAG,CAAC;MAC7B,IAAIf,KAAK,EAAEA,KAAK,CAAC6B,IAAI,CAACb,KAAK,CAAC,CAAC,KACxBZ,MAAM,CAAC0B,GAAG,CAACf,GAAG,EAAE,CAACC,KAAK,CAAC,CAAC;IAC/B;IACA,KAAK,MAAM,CAACD,GAAG,EAAEd,MAAM,CAAC,IAAIG,MAAM,EAAE;MAClCA,MAAM,CAAC0B,GAAG,CAACf,GAAG,EAAEW,OAAO,CAACzB,MAAM,EAAEO,CAAC,CAAC,CAAC;IACrC;IACA,OAAOM,GAAG,CAACV,MAAM,CAAC;EACpB,CAAC,CAAEH,MAAM,EAAE,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}