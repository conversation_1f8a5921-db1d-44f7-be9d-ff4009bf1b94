[{"C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\Dashboard.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\MarketData.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\StrategyManager.tsx": "6"}, {"size": 554, "mtime": 1752403988574, "results": "7", "hashOfConfig": "8"}, {"size": 425, "mtime": 1752403986856, "results": "9", "hashOfConfig": "8"}, {"size": 1209, "mtime": 1752404305997, "results": "10", "hashOfConfig": "8"}, {"size": 9398, "mtime": 1752404214415, "results": "11", "hashOfConfig": "8"}, {"size": 10802, "mtime": 1752404253233, "results": "12", "hashOfConfig": "8"}, {"size": 12619, "mtime": 1752404294403, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dzpwqd", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\App.tsx", ["32"], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\Dashboard.tsx", ["33"], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\MarketData.tsx", ["34"], [], "C:\\Users\\<USER>\\Desktop\\Python\\signal_stack\\frontend\\src\\components\\StrategyManager.tsx", ["35", "36", "37"], [], {"ruleId": "38", "severity": 1, "message": "39", "line": 1, "column": 17, "nodeType": "40", "messageId": "41", "endLine": 1, "endColumn": 25}, {"ruleId": "38", "severity": 1, "message": "42", "line": 57, "column": 26, "nodeType": "40", "messageId": "41", "endLine": 57, "endColumn": 43}, {"ruleId": "43", "severity": 1, "message": "44", "line": 57, "column": 6, "nodeType": "45", "endLine": 57, "endColumn": 48, "suggestions": "46"}, {"ruleId": "38", "severity": 1, "message": "47", "line": 4, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 4, "endColumn": 8}, {"ruleId": "38", "severity": 1, "message": "48", "line": 29, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 29, "endColumn": 12}, {"ruleId": "38", "severity": 1, "message": "49", "line": 30, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 30, "endColumn": 8}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'setSelectedSymbol' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterAndSortData'. Either include it or remove the dependency array.", "ArrayExpression", ["50"], "'Paper' is defined but never used.", "'PlayArrow' is defined but never used.", "'Pause' is defined but never used.", {"desc": "51", "fix": "52"}, "Update the dependencies array to be: [marketData, searchTerm, sortBy, filterBy, filterAndSortData]", {"range": "53", "text": "54"}, [1273, 1315], "[marketData, searchTerm, sortBy, filterBy, filterAndSortData]"]