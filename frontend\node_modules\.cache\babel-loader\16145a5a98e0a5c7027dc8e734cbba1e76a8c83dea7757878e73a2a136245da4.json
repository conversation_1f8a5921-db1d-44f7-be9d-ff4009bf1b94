{"ast": null, "code": "import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nexport const fontFamily = style({\n  prop: 'fontFamily',\n  themeKey: 'typography'\n});\nexport const fontSize = style({\n  prop: 'fontSize',\n  themeKey: 'typography'\n});\nexport const fontStyle = style({\n  prop: 'fontStyle',\n  themeKey: 'typography'\n});\nexport const fontWeight = style({\n  prop: 'fontWeight',\n  themeKey: 'typography'\n});\nexport const letterSpacing = style({\n  prop: 'letterSpacing'\n});\nexport const textTransform = style({\n  prop: 'textTransform'\n});\nexport const lineHeight = style({\n  prop: 'lineHeight'\n});\nexport const textAlign = style({\n  prop: 'textAlign'\n});\nexport const typographyVariant = style({\n  prop: 'typography',\n  cssProperty: false,\n  themeKey: 'typography'\n});\nconst typography = compose(typographyVariant, fontFamily, fontSize, fontStyle, fontWeight, letterSpacing, lineHeight, textAlign, textTransform);\nexport default typography;", "map": {"version": 3, "names": ["style", "compose", "fontFamily", "prop", "<PERSON><PERSON><PERSON>", "fontSize", "fontStyle", "fontWeight", "letterSpacing", "textTransform", "lineHeight", "textAlign", "typography<PERSON><PERSON><PERSON>", "cssProperty", "typography"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/system/esm/typography/typography.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nexport const fontFamily = style({\n  prop: 'fontFamily',\n  themeKey: 'typography'\n});\nexport const fontSize = style({\n  prop: 'fontSize',\n  themeKey: 'typography'\n});\nexport const fontStyle = style({\n  prop: 'fontStyle',\n  themeKey: 'typography'\n});\nexport const fontWeight = style({\n  prop: 'fontWeight',\n  themeKey: 'typography'\n});\nexport const letterSpacing = style({\n  prop: 'letterSpacing'\n});\nexport const textTransform = style({\n  prop: 'textTransform'\n});\nexport const lineHeight = style({\n  prop: 'lineHeight'\n});\nexport const textAlign = style({\n  prop: 'textAlign'\n});\nexport const typographyVariant = style({\n  prop: 'typography',\n  cssProperty: false,\n  themeKey: 'typography'\n});\nconst typography = compose(typographyVariant, fontFamily, fontSize, fontStyle, fontWeight, letterSpacing, lineHeight, textAlign, textTransform);\nexport default typography;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAO,MAAMC,UAAU,GAAGF,KAAK,CAAC;EAC9BG,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,MAAMC,QAAQ,GAAGL,KAAK,CAAC;EAC5BG,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,MAAME,SAAS,GAAGN,KAAK,CAAC;EAC7BG,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,MAAMG,UAAU,GAAGP,KAAK,CAAC;EAC9BG,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,MAAMI,aAAa,GAAGR,KAAK,CAAC;EACjCG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMM,aAAa,GAAGT,KAAK,CAAC;EACjCG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMO,UAAU,GAAGV,KAAK,CAAC;EAC9BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMQ,SAAS,GAAGX,KAAK,CAAC;EAC7BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMS,iBAAiB,GAAGZ,KAAK,CAAC;EACrCG,IAAI,EAAE,YAAY;EAClBU,WAAW,EAAE,KAAK;EAClBT,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMU,UAAU,GAAGb,OAAO,CAACW,iBAAiB,EAAEV,UAAU,EAAEG,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa,EAAEE,UAAU,EAAEC,SAAS,EAAEF,aAAa,CAAC;AAC/I,eAAeK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}