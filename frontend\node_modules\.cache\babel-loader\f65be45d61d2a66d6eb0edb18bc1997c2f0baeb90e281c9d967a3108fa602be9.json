{"ast": null, "code": "import isHostComponent from \"../isHostComponent/index.js\";\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nfunction appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return {\n    ...otherProps,\n    ownerState: {\n      ...otherProps.ownerState,\n      ...ownerState\n    }\n  };\n}\nexport default appendOwnerState;", "map": {"version": 3, "names": ["isHostComponent", "appendOwnerState", "elementType", "otherProps", "ownerState", "undefined"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/utils/esm/appendOwnerState/appendOwnerState.js"], "sourcesContent": ["import isHostComponent from \"../isHostComponent/index.js\";\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nfunction appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return {\n    ...otherProps,\n    ownerState: {\n      ...otherProps.ownerState,\n      ...ownerState\n    }\n  };\n}\nexport default appendOwnerState;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,6BAA6B;;AAEzD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAE;EAC7D,IAAIF,WAAW,KAAKG,SAAS,IAAIL,eAAe,CAACE,WAAW,CAAC,EAAE;IAC7D,OAAOC,UAAU;EACnB;EACA,OAAO;IACL,GAAGA,UAAU;IACbC,UAAU,EAAE;MACV,GAAGD,UAAU,CAACC,UAAU;MACxB,GAAGA;IACL;EACF,CAAC;AACH;AACA,eAAeH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}