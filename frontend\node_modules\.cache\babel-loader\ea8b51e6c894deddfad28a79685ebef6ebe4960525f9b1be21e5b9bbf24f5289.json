{"ast": null, "code": "export function x(p) {\n  return p[0];\n}\nexport function y(p) {\n  return p[1];\n}", "map": {"version": 3, "names": ["x", "p", "y"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-shape/src/point.js"], "sourcesContent": ["export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n"], "mappings": "AAAA,OAAO,SAASA,CAACA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,OAAO,SAASC,CAACA,CAACD,CAAC,EAAE;EACnB,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}