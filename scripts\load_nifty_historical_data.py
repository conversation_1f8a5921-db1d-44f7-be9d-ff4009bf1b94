#!/usr/bin/env python3
"""
Comprehensive NIFTY Historical Data Loading Script
Loads 3 months of 1-minute NIFTY data with enhanced error handling and validation.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
import argparse

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db, check_database_connection
from app.services.historical_data_loader import HistoricalDataLoader, LoadingProgress
from app.services.symbol_mapping_service import SymbolMappingService
from app.services.data_service import DataService

logger = get_logger(__name__)

class NIFTYDataLoadManager:
    """Manager for NIFTY historical data loading operations."""
    
    def __init__(self):
        """Initialize the manager."""
        self.db = None
        self.loader = None
        self.symbol_mapping_service = None
        self.data_service = None
        
    def initialize_services(self) -> bool:
        """Initialize all required services."""
        try:
            # Check database connection
            if not check_database_connection():
                logger.error("Database connection failed")
                return False
            
            # Initialize database session
            self.db = next(get_db())
            
            # Initialize services
            self.loader = HistoricalDataLoader(self.db)
            self.symbol_mapping_service = SymbolMappingService()
            self.data_service = DataService(self.db)
            
            # Set progress callback
            self.loader.set_progress_callback(self.progress_callback)
            
            logger.info("✓ Services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def progress_callback(self, progress: LoadingProgress):
        """Callback for progress updates."""
        if progress.loaded_records % 1000 == 0:  # Log every 1000 records
            logger.info(f"Progress: {progress.progress_percent:.1f}% "
                       f"({progress.loaded_records}/{progress.total_records}) "
                       f"- {progress.records_per_second:.1f} records/sec")
    
    def validate_prerequisites(self) -> bool:
        """Validate prerequisites for data loading."""
        try:
            logger.info("🔍 Validating prerequisites...")
            
            # Check symbol mapping service
            logger.info("   Checking symbol mapping service...")
            mapping = self.symbol_mapping_service.get_symbol_mapping("NIFTY")
            if not mapping:
                logger.error("   ✗ NIFTY symbol mapping not found")
                return False
            logger.info(f"   ✓ NIFTY mapping found: {mapping.fyers_symbol}")
            
            # Check Fyers authentication
            logger.info("   Testing Fyers authentication...")
            if not self.loader.authenticate_fyers():
                logger.error("   ✗ Fyers authentication failed")
                return False
            logger.info("   ✓ Fyers authentication successful")
            
            logger.info("✓ All prerequisites validated")
            return True
            
        except Exception as e:
            logger.error(f"Error validating prerequisites: {e}")
            return False
    
    def check_existing_data(self) -> dict:
        """Check existing NIFTY data in database."""
        try:
            logger.info("📊 Checking existing data...")
            
            stats = self.data_service.get_data_statistics("NIFTY")
            if stats and stats.get('total_records', 0) > 0:
                logger.info(f"   Existing records: {stats['total_records']:,}")
                logger.info(f"   Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
                logger.info(f"   Latest price: ₹{stats['latest_price']}")
                return stats
            else:
                logger.info("   No existing data found")
                return {}
                
        except Exception as e:
            logger.error(f"Error checking existing data: {e}")
            return {}
    
    def load_historical_data(self, force_refresh: bool = False) -> bool:
        """Load 3 months of NIFTY historical data."""
        try:
            logger.info("🚀 Starting NIFTY historical data loading...")
            logger.info("=" * 60)
            
            # Check existing data
            existing_stats = self.check_existing_data()
            
            if existing_stats and not force_refresh:
                response = input("\nExisting data found. Continue and potentially overwrite? (y/N): ")
                if response.lower() != 'y':
                    logger.info("Operation cancelled by user")
                    return False
            
            # Load data
            logger.info("\n📥 Loading 3 months of 1-minute NIFTY data...")
            start_time = datetime.now()
            
            result = self.loader.load_nifty_3months()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            # Display results
            logger.info("\n" + "=" * 60)
            logger.info("📈 LOADING RESULTS")
            logger.info("=" * 60)
            logger.info(f"Symbol: {result.symbol}")
            logger.info(f"Success: {'✅ YES' if result.success else '❌ NO'}")
            logger.info(f"Records loaded: {result.records_loaded:,}")
            logger.info(f"Records skipped: {result.records_skipped:,}")
            logger.info(f"Duration: {duration.total_seconds():.1f} seconds")
            
            if result.errors:
                logger.warning(f"Errors encountered: {len(result.errors)}")
                for error in result.errors[:5]:  # Show first 5 errors
                    logger.warning(f"  - {error}")
            
            return result.success
            
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
            return False
    
    def validate_loaded_data(self) -> bool:
        """Validate the loaded data."""
        try:
            logger.info("\n🔍 Validating loaded data...")
            
            # Get updated statistics
            stats = self.data_service.get_data_statistics("NIFTY")
            if not stats:
                logger.error("No data found for validation")
                return False
            
            logger.info("📊 Data Statistics:")
            logger.info(f"   Total records: {stats['total_records']:,}")
            logger.info(f"   Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
            logger.info(f"   Latest price: ₹{stats['latest_price']}")
            
            # Calculate expected records (rough estimate)
            expected_days = 90  # 3 months
            trading_minutes_per_day = 375  # 9:15 AM to 3:30 PM
            expected_records = expected_days * trading_minutes_per_day
            
            actual_records = stats['total_records']
            completeness = (actual_records / expected_records) * 100
            
            logger.info(f"📈 Data Completeness:")
            logger.info(f"   Expected records (approx): {expected_records:,}")
            logger.info(f"   Actual records: {actual_records:,}")
            logger.info(f"   Completeness: {completeness:.1f}%")
            
            # Check for data gaps
            gaps = self.data_service.check_data_gaps("NIFTY", timedelta(minutes=5))
            if gaps:
                logger.warning(f"⚠️  Found {len(gaps)} data gaps larger than 5 minutes")
                for gap in gaps[:3]:  # Show first 3 gaps
                    logger.warning(f"   Gap: {gap['start']} to {gap['end']} ({gap['duration']})")
            else:
                logger.info("✓ No significant data gaps found")
            
            # Validation criteria
            min_completeness = 70.0  # Minimum 70% completeness
            max_gaps = 50  # Maximum 50 gaps allowed
            
            is_valid = (completeness >= min_completeness and len(gaps) <= max_gaps)
            
            if is_valid:
                logger.info("✅ Data validation PASSED")
            else:
                logger.warning("⚠️  Data validation has concerns but data is usable")
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating data: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Load NIFTY historical data")
    parser.add_argument("--force", action="store_true", 
                       help="Force refresh even if data exists")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate existing data")
    
    args = parser.parse_args()
    
    logger.info("🚀 NIFTY Historical Data Loading Script")
    logger.info("=" * 60)
    
    manager = NIFTYDataLoadManager()
    success = False
    
    try:
        # Step 1: Initialize services
        logger.info("Step 1: Initializing services...")
        if not manager.initialize_services():
            return False
        
        # Step 2: Validate prerequisites
        if not args.validate_only:
            logger.info("\nStep 2: Validating prerequisites...")
            if not manager.validate_prerequisites():
                return False
        
        # Step 3: Load or validate data
        if args.validate_only:
            logger.info("\nStep 3: Validating existing data...")
            success = manager.validate_loaded_data()
        else:
            logger.info("\nStep 3: Loading historical data...")
            if manager.load_historical_data(args.force):
                logger.info("\nStep 4: Validating loaded data...")
                success = manager.validate_loaded_data()
        
        if success:
            logger.info("\n🎉 Operation completed successfully!")
            logger.info("✓ NIFTY historical data is ready for analysis")
        else:
            logger.error("\n❌ Operation failed or has issues")
        
        return success
        
    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation cancelled by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        manager.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
