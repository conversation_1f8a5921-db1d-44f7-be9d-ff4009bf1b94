{"ast": null, "code": "'use strict';\n\nvar toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};", "map": {"version": 3, "names": ["toPrimitive", "require", "isSymbol", "module", "exports", "argument", "key"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/core-js-pure/internals/to-property-key.js"], "sourcesContent": ["'use strict';\nvar toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACtD,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;;AAEhD;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIC,GAAG,GAAGN,WAAW,CAACK,QAAQ,EAAE,QAAQ,CAAC;EACzC,OAAOH,QAAQ,CAACI,GAAG,CAAC,GAAGA,GAAG,GAAGA,GAAG,GAAG,EAAE;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}