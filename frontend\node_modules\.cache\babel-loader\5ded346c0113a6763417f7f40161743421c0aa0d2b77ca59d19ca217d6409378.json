{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport get from 'es-toolkit/compat/get';\nimport { selectLegendSettings, selectLegendSize } from './legendSelectors';\nimport { appendOffsetOfLegend } from '../../util/ChartUtils';\nimport { selectChartHeight, selectChartWidth, selectMargin } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nexport var selectBrushHeight = state => state.brush.height;\n\n/**\n * For internal use only.\n *\n * @param root state\n * @return ChartOffsetInternal\n */\nexport var selectChartOffsetInternal = createSelector([selectChartWidth, selectChartHeight, selectMargin, selectBrushHeight, selectAllXAxes, selectAllYAxes, selectLegendSettings, selectLegendSize], (chartWidth, chartHeight, margin, brushHeight, xAxes, yAxes, legendSettings, legendSize) => {\n  var offsetH = yAxes.reduce((result, entry) => {\n    var {\n      orientation\n    } = entry;\n    if (!entry.mirror && !entry.hide) {\n      var width = typeof entry.width === 'number' ? entry.width : DEFAULT_Y_AXIS_WIDTH;\n      return _objectSpread(_objectSpread({}, result), {}, {\n        [orientation]: result[orientation] + width\n      });\n    }\n    return result;\n  }, {\n    left: margin.left || 0,\n    right: margin.right || 0\n  });\n  var offsetV = xAxes.reduce((result, entry) => {\n    var {\n      orientation\n    } = entry;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, {\n        [orientation]: get(result, \"\".concat(orientation)) + entry.height\n      });\n    }\n    return result;\n  }, {\n    top: margin.top || 0,\n    bottom: margin.bottom || 0\n  });\n  var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n  var brushBottom = offset.bottom;\n  offset.bottom += brushHeight;\n  offset = appendOffsetOfLegend(offset, legendSettings, legendSize);\n  var offsetWidth = chartWidth - offset.left - offset.right;\n  var offsetHeight = chartHeight - offset.top - offset.bottom;\n  return _objectSpread(_objectSpread({\n    brushBottom\n  }, offset), {}, {\n    // never return negative values for height and width\n    width: Math.max(offsetWidth, 0),\n    height: Math.max(offsetHeight, 0)\n  });\n});\nexport var selectChartViewBox = createSelector(selectChartOffsetInternal, offset => ({\n  x: offset.left,\n  y: offset.top,\n  width: offset.width,\n  height: offset.height\n}));\nexport var selectAxisViewBox = createSelector(selectChartWidth, selectChartHeight, (width, height) => ({\n  x: 0,\n  y: 0,\n  width,\n  height\n}));", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "get", "selectLegendSettings", "selectLegendSize", "appendOffsetOfLegend", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "selectAllXAxes", "selectAllYAxes", "DEFAULT_Y_AXIS_WIDTH", "selectBrushHeight", "state", "brush", "height", "selectChartOffsetInternal", "chartWidth", "chartHeight", "margin", "brushHeight", "xAxes", "yAxes", "legendSettings", "legendSize", "offsetH", "reduce", "result", "entry", "orientation", "mirror", "hide", "width", "left", "right", "offsetV", "concat", "top", "bottom", "offset", "brushBottom", "offsetWidth", "offsetHeight", "Math", "max", "selectChartViewBox", "x", "y", "selectAxisViewBox"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/recharts/es6/state/selectors/selectChartOffsetInternal.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport get from 'es-toolkit/compat/get';\nimport { selectLegendSettings, selectLegendSize } from './legendSelectors';\nimport { appendOffsetOfLegend } from '../../util/ChartUtils';\nimport { selectChartHeight, selectChartWidth, selectMargin } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nexport var selectBrushHeight = state => state.brush.height;\n\n/**\n * For internal use only.\n *\n * @param root state\n * @return ChartOffsetInternal\n */\nexport var selectChartOffsetInternal = createSelector([selectChartWidth, selectChartHeight, selectMargin, selectBrushHeight, selectAllXAxes, selectAllYAxes, selectLegendSettings, selectLegendSize], (chartWidth, chartHeight, margin, brushHeight, xAxes, yAxes, legendSettings, legendSize) => {\n  var offsetH = yAxes.reduce((result, entry) => {\n    var {\n      orientation\n    } = entry;\n    if (!entry.mirror && !entry.hide) {\n      var width = typeof entry.width === 'number' ? entry.width : DEFAULT_Y_AXIS_WIDTH;\n      return _objectSpread(_objectSpread({}, result), {}, {\n        [orientation]: result[orientation] + width\n      });\n    }\n    return result;\n  }, {\n    left: margin.left || 0,\n    right: margin.right || 0\n  });\n  var offsetV = xAxes.reduce((result, entry) => {\n    var {\n      orientation\n    } = entry;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, {\n        [orientation]: get(result, \"\".concat(orientation)) + entry.height\n      });\n    }\n    return result;\n  }, {\n    top: margin.top || 0,\n    bottom: margin.bottom || 0\n  });\n  var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n  var brushBottom = offset.bottom;\n  offset.bottom += brushHeight;\n  offset = appendOffsetOfLegend(offset, legendSettings, legendSize);\n  var offsetWidth = chartWidth - offset.left - offset.right;\n  var offsetHeight = chartHeight - offset.top - offset.bottom;\n  return _objectSpread(_objectSpread({\n    brushBottom\n  }, offset), {}, {\n    // never return negative values for height and width\n    width: Math.max(offsetWidth, 0),\n    height: Math.max(offsetHeight, 0)\n  });\n});\nexport var selectChartViewBox = createSelector(selectChartOffsetInternal, offset => ({\n  x: offset.left,\n  y: offset.top,\n  width: offset.width,\n  height: offset.height\n}));\nexport var selectAxisViewBox = createSelector(selectChartWidth, selectChartHeight, (width, height) => ({\n  x: 0,\n  y: 0,\n  width,\n  height\n}));"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,oBAAoB,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC1E,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,sBAAsB;AACxF,SAASC,cAAc,EAAEC,cAAc,QAAQ,iBAAiB;AAChE,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,OAAO,IAAIC,iBAAiB,GAAGC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,yBAAyB,GAAGf,cAAc,CAAC,CAACM,gBAAgB,EAAED,iBAAiB,EAAEE,YAAY,EAAEI,iBAAiB,EAAEH,cAAc,EAAEC,cAAc,EAAEP,oBAAoB,EAAEC,gBAAgB,CAAC,EAAE,CAACa,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAEC,cAAc,EAAEC,UAAU,KAAK;EAChS,IAAIC,OAAO,GAAGH,KAAK,CAACI,MAAM,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC5C,IAAI;MACFC;IACF,CAAC,GAAGD,KAAK;IACT,IAAI,CAACA,KAAK,CAACE,MAAM,IAAI,CAACF,KAAK,CAACG,IAAI,EAAE;MAChC,IAAIC,KAAK,GAAG,OAAOJ,KAAK,CAACI,KAAK,KAAK,QAAQ,GAAGJ,KAAK,CAACI,KAAK,GAAGrB,oBAAoB;MAChF,OAAO9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8C,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAClD,CAACE,WAAW,GAAGF,MAAM,CAACE,WAAW,CAAC,GAAGG;MACvC,CAAC,CAAC;IACJ;IACA,OAAOL,MAAM;EACf,CAAC,EAAE;IACDM,IAAI,EAAEd,MAAM,CAACc,IAAI,IAAI,CAAC;IACtBC,KAAK,EAAEf,MAAM,CAACe,KAAK,IAAI;EACzB,CAAC,CAAC;EACF,IAAIC,OAAO,GAAGd,KAAK,CAACK,MAAM,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC5C,IAAI;MACFC;IACF,CAAC,GAAGD,KAAK;IACT,IAAI,CAACA,KAAK,CAACE,MAAM,IAAI,CAACF,KAAK,CAACG,IAAI,EAAE;MAChC,OAAOlD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8C,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAClD,CAACE,WAAW,GAAG3B,GAAG,CAACyB,MAAM,EAAE,EAAE,CAACS,MAAM,CAACP,WAAW,CAAC,CAAC,GAAGD,KAAK,CAACb;MAC7D,CAAC,CAAC;IACJ;IACA,OAAOY,MAAM;EACf,CAAC,EAAE;IACDU,GAAG,EAAElB,MAAM,CAACkB,GAAG,IAAI,CAAC;IACpBC,MAAM,EAAEnB,MAAM,CAACmB,MAAM,IAAI;EAC3B,CAAC,CAAC;EACF,IAAIC,MAAM,GAAG1D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsD,OAAO,CAAC,EAAEV,OAAO,CAAC;EAC/D,IAAIe,WAAW,GAAGD,MAAM,CAACD,MAAM;EAC/BC,MAAM,CAACD,MAAM,IAAIlB,WAAW;EAC5BmB,MAAM,GAAGlC,oBAAoB,CAACkC,MAAM,EAAEhB,cAAc,EAAEC,UAAU,CAAC;EACjE,IAAIiB,WAAW,GAAGxB,UAAU,GAAGsB,MAAM,CAACN,IAAI,GAAGM,MAAM,CAACL,KAAK;EACzD,IAAIQ,YAAY,GAAGxB,WAAW,GAAGqB,MAAM,CAACF,GAAG,GAAGE,MAAM,CAACD,MAAM;EAC3D,OAAOzD,aAAa,CAACA,aAAa,CAAC;IACjC2D;EACF,CAAC,EAAED,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;IACd;IACAP,KAAK,EAAEW,IAAI,CAACC,GAAG,CAACH,WAAW,EAAE,CAAC,CAAC;IAC/B1B,MAAM,EAAE4B,IAAI,CAACC,GAAG,CAACF,YAAY,EAAE,CAAC;EAClC,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,OAAO,IAAIG,kBAAkB,GAAG5C,cAAc,CAACe,yBAAyB,EAAEuB,MAAM,KAAK;EACnFO,CAAC,EAAEP,MAAM,CAACN,IAAI;EACdc,CAAC,EAAER,MAAM,CAACF,GAAG;EACbL,KAAK,EAAEO,MAAM,CAACP,KAAK;EACnBjB,MAAM,EAAEwB,MAAM,CAACxB;AACjB,CAAC,CAAC,CAAC;AACH,OAAO,IAAIiC,iBAAiB,GAAG/C,cAAc,CAACM,gBAAgB,EAAED,iBAAiB,EAAE,CAAC0B,KAAK,EAAEjB,MAAM,MAAM;EACrG+B,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJf,KAAK;EACLjB;AACF,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}