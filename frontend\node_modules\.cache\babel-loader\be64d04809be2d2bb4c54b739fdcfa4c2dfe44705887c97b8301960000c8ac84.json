{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { isNan } from '../util/DataUtils';\n\n/**\n * These chart options are decided internally, by Recharts,\n * and will not change during the lifetime of the chart.\n *\n * Changing these options can be done by swapping the root element\n * which will make a brand-new Redux store.\n *\n * If you want to store options that can be changed by the user,\n * use UpdatableChartOptions in rootPropsSlice.ts.\n */\n\nexport function arrayTooltipSearcher(data, strIndex) {\n  if (!strIndex) return undefined;\n  var numIndex = Number.parseInt(strIndex, 10);\n  if (isNan(numIndex)) {\n    return undefined;\n  }\n  return data === null || data === void 0 ? void 0 : data[numIndex];\n}\nvar initialState = {\n  chartName: '',\n  tooltipPayloadSearcher: undefined,\n  eventEmitter: undefined,\n  defaultTooltipEventType: 'axis'\n};\nvar optionsSlice = createSlice({\n  name: 'options',\n  initialState,\n  reducers: {\n    createEventEmitter: state => {\n      if (state.eventEmitter == null) {\n        state.eventEmitter = Symbol('rechartsEventEmitter');\n      }\n    }\n  }\n});\nexport var optionsReducer = optionsSlice.reducer;\nexport var {\n  createEventEmitter\n} = optionsSlice.actions;", "map": {"version": 3, "names": ["createSlice", "isNan", "arrayTooltipSearcher", "data", "strIndex", "undefined", "numIndex", "Number", "parseInt", "initialState", "chartName", "tooltipPayloadSearcher", "eventEmitter", "defaultTooltipEventType", "optionsSlice", "name", "reducers", "createEventEmitter", "state", "Symbol", "optionsReducer", "reducer", "actions"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/recharts/es6/state/optionsSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nimport { isNan } from '../util/DataUtils';\n\n/**\n * These chart options are decided internally, by Recharts,\n * and will not change during the lifetime of the chart.\n *\n * Changing these options can be done by swapping the root element\n * which will make a brand-new Redux store.\n *\n * If you want to store options that can be changed by the user,\n * use UpdatableChartOptions in rootPropsSlice.ts.\n */\n\nexport function arrayTooltipSearcher(data, strIndex) {\n  if (!strIndex) return undefined;\n  var numIndex = Number.parseInt(strIndex, 10);\n  if (isNan(numIndex)) {\n    return undefined;\n  }\n  return data === null || data === void 0 ? void 0 : data[numIndex];\n}\nvar initialState = {\n  chartName: '',\n  tooltipPayloadSearcher: undefined,\n  eventEmitter: undefined,\n  defaultTooltipEventType: 'axis'\n};\nvar optionsSlice = createSlice({\n  name: 'options',\n  initialState,\n  reducers: {\n    createEventEmitter: state => {\n      if (state.eventEmitter == null) {\n        state.eventEmitter = Symbol('rechartsEventEmitter');\n      }\n    }\n  }\n});\nexport var optionsReducer = optionsSlice.reducer;\nexport var {\n  createEventEmitter\n} = optionsSlice.actions;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,mBAAmB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACnD,IAAI,CAACA,QAAQ,EAAE,OAAOC,SAAS;EAC/B,IAAIC,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACJ,QAAQ,EAAE,EAAE,CAAC;EAC5C,IAAIH,KAAK,CAACK,QAAQ,CAAC,EAAE;IACnB,OAAOD,SAAS;EAClB;EACA,OAAOF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,QAAQ,CAAC;AACnE;AACA,IAAIG,YAAY,GAAG;EACjBC,SAAS,EAAE,EAAE;EACbC,sBAAsB,EAAEN,SAAS;EACjCO,YAAY,EAAEP,SAAS;EACvBQ,uBAAuB,EAAE;AAC3B,CAAC;AACD,IAAIC,YAAY,GAAGd,WAAW,CAAC;EAC7Be,IAAI,EAAE,SAAS;EACfN,YAAY;EACZO,QAAQ,EAAE;IACRC,kBAAkB,EAAEC,KAAK,IAAI;MAC3B,IAAIA,KAAK,CAACN,YAAY,IAAI,IAAI,EAAE;QAC9BM,KAAK,CAACN,YAAY,GAAGO,MAAM,CAAC,sBAAsB,CAAC;MACrD;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAIC,cAAc,GAAGN,YAAY,CAACO,OAAO;AAChD,OAAO,IAAI;EACTJ;AACF,CAAC,GAAGH,YAAY,CAACQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}