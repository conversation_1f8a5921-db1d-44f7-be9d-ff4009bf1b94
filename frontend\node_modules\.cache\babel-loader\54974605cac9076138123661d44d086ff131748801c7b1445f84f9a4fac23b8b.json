{"ast": null, "code": "/**\n * @fileOverview Some common arithmetic methods\n * <AUTHOR>\n * @date 2015-09-17\n */\nimport Decimal from 'decimal.js-light';\nimport { curry } from './utils';\n\n/**\n * Get the digit count of a number.\n * If the absolute value is in the interval [0.1, 1), the result is 0.\n * If the absolute value is in the interval [0.01, 0.1), the digit count is -1.\n * If the absolute value is in the interval [0.001, 0.01), the digit count is -2.\n *\n * @param  {Number} value The number\n * @return {Integer}      Digit count\n */\nfunction getDigitCount(value) {\n  var result;\n  if (value === 0) {\n    result = 1;\n  } else {\n    result = Math.floor(new Decimal(value).abs().log(10).toNumber()) + 1;\n  }\n  return result;\n}\n\n/**\n * Get the data in the interval [start, end) with a fixed step.\n * Also handles JS calculation precision issues.\n *\n * @param  {Decimal} start Start point\n * @param  {Decimal} end   End point, not included\n * @param  {Decimal} step  Step size\n * @return {Array}         Array of numbers\n */\nfunction rangeStep(start, end, step) {\n  var num = new Decimal(start);\n  var i = 0;\n  var result = [];\n\n  // magic number to prevent infinite loop\n  while (num.lt(end) && i < 100000) {\n    result.push(num.toNumber());\n    num = num.add(step);\n    i++;\n  }\n  return result;\n}\n\n/**\n * Linear interpolation of numbers.\n *\n * @param  {Number} a  Endpoint of the domain\n * @param  {Number} b  Endpoint of the domain\n * @param  {Number} t  A value in [0, 1]\n * @return {Number}    A value in the domain\n */\nvar interpolateNumber = curry((a, b, t) => {\n  var newA = +a;\n  var newB = +b;\n  return newA + t * (newB - newA);\n});\n\n/**\n * Inverse operation of linear interpolation.\n *\n * @param  {Number} a Endpoint of the domain\n * @param  {Number} b Endpoint of the domain\n * @param  {Number} x Can be considered as an output value after interpolation\n * @return {Number}   When x is in the range a ~ b, the return value is in [0, 1]\n */\nvar uninterpolateNumber = curry((a, b, x) => {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return (x - a) / diff;\n});\n\n/**\n * Inverse operation of linear interpolation with truncation.\n *\n * @param  {Number} a Endpoint of the domain\n * @param  {Number} b Endpoint of the domain\n * @param  {Number} x Can be considered as an output value after interpolation\n * @return {Number}   When x is in the interval a ~ b, the return value is in [0, 1].\n *                    When x is not in the interval a ~ b, it will be truncated to the interval a ~ b.\n */\nvar uninterpolateTruncation = curry((a, b, x) => {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return Math.max(0, Math.min(1, (x - a) / diff));\n});\nexport { rangeStep, getDigitCount, interpolateNumber, uninterpolateNumber, uninterpolateTruncation };", "map": {"version": 3, "names": ["Decimal", "curry", "getDigitCount", "value", "result", "Math", "floor", "abs", "log", "toNumber", "rangeStep", "start", "end", "step", "num", "i", "lt", "push", "add", "interpolateNumber", "a", "b", "t", "newA", "newB", "uninterpolateNumber", "x", "diff", "Infinity", "uninterpolateTruncation", "max", "min"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/recharts/es6/util/scale/util/arithmetic.js"], "sourcesContent": ["/**\n * @fileOverview Some common arithmetic methods\n * <AUTHOR>\n * @date 2015-09-17\n */\nimport Decimal from 'decimal.js-light';\nimport { curry } from './utils';\n\n/**\n * Get the digit count of a number.\n * If the absolute value is in the interval [0.1, 1), the result is 0.\n * If the absolute value is in the interval [0.01, 0.1), the digit count is -1.\n * If the absolute value is in the interval [0.001, 0.01), the digit count is -2.\n *\n * @param  {Number} value The number\n * @return {Integer}      Digit count\n */\nfunction getDigitCount(value) {\n  var result;\n  if (value === 0) {\n    result = 1;\n  } else {\n    result = Math.floor(new Decimal(value).abs().log(10).toNumber()) + 1;\n  }\n  return result;\n}\n\n/**\n * Get the data in the interval [start, end) with a fixed step.\n * Also handles JS calculation precision issues.\n *\n * @param  {Decimal} start Start point\n * @param  {Decimal} end   End point, not included\n * @param  {Decimal} step  Step size\n * @return {Array}         Array of numbers\n */\nfunction rangeStep(start, end, step) {\n  var num = new Decimal(start);\n  var i = 0;\n  var result = [];\n\n  // magic number to prevent infinite loop\n  while (num.lt(end) && i < 100000) {\n    result.push(num.toNumber());\n    num = num.add(step);\n    i++;\n  }\n  return result;\n}\n\n/**\n * Linear interpolation of numbers.\n *\n * @param  {Number} a  Endpoint of the domain\n * @param  {Number} b  Endpoint of the domain\n * @param  {Number} t  A value in [0, 1]\n * @return {Number}    A value in the domain\n */\nvar interpolateNumber = curry((a, b, t) => {\n  var newA = +a;\n  var newB = +b;\n  return newA + t * (newB - newA);\n});\n\n/**\n * Inverse operation of linear interpolation.\n *\n * @param  {Number} a Endpoint of the domain\n * @param  {Number} b Endpoint of the domain\n * @param  {Number} x Can be considered as an output value after interpolation\n * @return {Number}   When x is in the range a ~ b, the return value is in [0, 1]\n */\nvar uninterpolateNumber = curry((a, b, x) => {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return (x - a) / diff;\n});\n\n/**\n * Inverse operation of linear interpolation with truncation.\n *\n * @param  {Number} a Endpoint of the domain\n * @param  {Number} b Endpoint of the domain\n * @param  {Number} x Can be considered as an output value after interpolation\n * @return {Number}   When x is in the interval a ~ b, the return value is in [0, 1].\n *                    When x is not in the interval a ~ b, it will be truncated to the interval a ~ b.\n */\nvar uninterpolateTruncation = curry((a, b, x) => {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return Math.max(0, Math.min(1, (x - a) / diff));\n});\nexport { rangeStep, getDigitCount, interpolateNumber, uninterpolateNumber, uninterpolateTruncation };"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAOA,OAAO,MAAM,kBAAkB;AACtC,SAASC,KAAK,QAAQ,SAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIC,MAAM;EACV,IAAID,KAAK,KAAK,CAAC,EAAE;IACfC,MAAM,GAAG,CAAC;EACZ,CAAC,MAAM;IACLA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAIN,OAAO,CAACG,KAAK,CAAC,CAACI,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;EACtE;EACA,OAAOL,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnC,IAAIC,GAAG,GAAG,IAAId,OAAO,CAACW,KAAK,CAAC;EAC5B,IAAII,CAAC,GAAG,CAAC;EACT,IAAIX,MAAM,GAAG,EAAE;;EAEf;EACA,OAAOU,GAAG,CAACE,EAAE,CAACJ,GAAG,CAAC,IAAIG,CAAC,GAAG,MAAM,EAAE;IAChCX,MAAM,CAACa,IAAI,CAACH,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC;IAC3BK,GAAG,GAAGA,GAAG,CAACI,GAAG,CAACL,IAAI,CAAC;IACnBE,CAAC,EAAE;EACL;EACA,OAAOX,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIe,iBAAiB,GAAGlB,KAAK,CAAC,CAACmB,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EACzC,IAAIC,IAAI,GAAG,CAACH,CAAC;EACb,IAAII,IAAI,GAAG,CAACH,CAAC;EACb,OAAOE,IAAI,GAAGD,CAAC,IAAIE,IAAI,GAAGD,IAAI,CAAC;AACjC,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,mBAAmB,GAAGxB,KAAK,CAAC,CAACmB,CAAC,EAAEC,CAAC,EAAEK,CAAC,KAAK;EAC3C,IAAIC,IAAI,GAAGN,CAAC,GAAG,CAACD,CAAC;EACjBO,IAAI,GAAGA,IAAI,IAAIC,QAAQ;EACvB,OAAO,CAACF,CAAC,GAAGN,CAAC,IAAIO,IAAI;AACvB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,uBAAuB,GAAG5B,KAAK,CAAC,CAACmB,CAAC,EAAEC,CAAC,EAAEK,CAAC,KAAK;EAC/C,IAAIC,IAAI,GAAGN,CAAC,GAAG,CAACD,CAAC;EACjBO,IAAI,GAAGA,IAAI,IAAIC,QAAQ;EACvB,OAAOvB,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAEzB,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,CAACL,CAAC,GAAGN,CAAC,IAAIO,IAAI,CAAC,CAAC;AACjD,CAAC,CAAC;AACF,SAASjB,SAAS,EAAER,aAAa,EAAEiB,iBAAiB,EAAEM,mBAAmB,EAAEI,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}