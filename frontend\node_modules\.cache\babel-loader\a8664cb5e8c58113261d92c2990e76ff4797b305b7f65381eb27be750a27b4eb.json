{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isLength(value) {\n  return Number.isSafeInteger(value) && value >= 0;\n}\nexports.isLength = isLength;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isSafeInteger"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/es-toolkit/dist/predicate/isLength.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isLength(value) {\n    return Number.isSafeInteger(value) && value >= 0;\n}\n\nexports.isLength = isLength;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,QAAQA,CAACD,KAAK,EAAE;EACrB,OAAOE,MAAM,CAACC,aAAa,CAACH,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC;AACpD;AAEAH,OAAO,CAACI,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}