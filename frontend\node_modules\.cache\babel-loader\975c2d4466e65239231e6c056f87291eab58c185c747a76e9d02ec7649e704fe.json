{"ast": null, "code": "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand = Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n  this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n  this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n  this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n  this.jitter = jitter;\n};", "map": {"version": 3, "names": ["Backoff", "opts", "ms", "min", "max", "factor", "jitter", "attempts", "prototype", "duration", "Math", "pow", "rand", "random", "deviation", "floor", "reset", "setMin", "setMax", "setJitter"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/socket.io-client/build/esm/contrib/backo2.js"], "sourcesContent": ["/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,IAAI,EAAE;EAC1BA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjB,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACE,GAAG,IAAI,GAAG;EACzB,IAAI,CAACC,GAAG,GAAGH,IAAI,CAACG,GAAG,IAAI,KAAK;EAC5B,IAAI,CAACC,MAAM,GAAGJ,IAAI,CAACI,MAAM,IAAI,CAAC;EAC9B,IAAI,CAACC,MAAM,GAAGL,IAAI,CAACK,MAAM,GAAG,CAAC,IAAIL,IAAI,CAACK,MAAM,IAAI,CAAC,GAAGL,IAAI,CAACK,MAAM,GAAG,CAAC;EACnE,IAAI,CAACC,QAAQ,GAAG,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACAP,OAAO,CAACQ,SAAS,CAACC,QAAQ,GAAG,YAAY;EACrC,IAAIP,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGQ,IAAI,CAACC,GAAG,CAAC,IAAI,CAACN,MAAM,EAAE,IAAI,CAACE,QAAQ,EAAE,CAAC;EACzD,IAAI,IAAI,CAACD,MAAM,EAAE;IACb,IAAIM,IAAI,GAAGF,IAAI,CAACG,MAAM,CAAC,CAAC;IACxB,IAAIC,SAAS,GAAGJ,IAAI,CAACK,KAAK,CAACH,IAAI,GAAG,IAAI,CAACN,MAAM,GAAGJ,EAAE,CAAC;IACnDA,EAAE,GAAG,CAACQ,IAAI,CAACK,KAAK,CAACH,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGV,EAAE,GAAGY,SAAS,GAAGZ,EAAE,GAAGY,SAAS;EAC3E;EACA,OAAOJ,IAAI,CAACP,GAAG,CAACD,EAAE,EAAE,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC;AACrC,CAAC;AACD;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACQ,SAAS,CAACQ,KAAK,GAAG,YAAY;EAClC,IAAI,CAACT,QAAQ,GAAG,CAAC;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACAP,OAAO,CAACQ,SAAS,CAACS,MAAM,GAAG,UAAUd,GAAG,EAAE;EACtC,IAAI,CAACD,EAAE,GAAGC,GAAG;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACAH,OAAO,CAACQ,SAAS,CAACU,MAAM,GAAG,UAAUd,GAAG,EAAE;EACtC,IAAI,CAACA,GAAG,GAAGA,GAAG;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACAJ,OAAO,CAACQ,SAAS,CAACW,SAAS,GAAG,UAAUb,MAAM,EAAE;EAC5C,IAAI,CAACA,MAAM,GAAGA,MAAM;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}