{"ast": null, "code": "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" && typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n  // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n  // script, so we create one single event listener here which will forward the event to the socket instances\n  addEventListener(\"offline\", () => {\n    OFFLINE_EVENT_LISTENERS.forEach(listener => listener());\n  }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri - uri or options\n   * @param {Object} opts - options\n   */\n  constructor(uri, opts) {\n    super();\n    this.binaryType = defaultBinaryType;\n    this.writeBuffer = [];\n    this._prevBufferLen = 0;\n    this._pingInterval = -1;\n    this._pingTimeout = -1;\n    this._maxPayload = -1;\n    /**\n     * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n     * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n     */\n    this._pingTimeoutTime = Infinity;\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n    if (uri) {\n      const parsedUri = parse(uri);\n      opts.hostname = parsedUri.host;\n      opts.secure = parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n      opts.port = parsedUri.port;\n      if (parsedUri.query) opts.query = parsedUri.query;\n    } else if (opts.host) {\n      opts.hostname = parse(opts.host).host;\n    }\n    installTimerFunctions(this, opts);\n    this.secure = null != opts.secure ? opts.secure : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n    this.hostname = opts.hostname || (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port = opts.port || (typeof location !== \"undefined\" && location.port ? location.port : this.secure ? \"443\" : \"80\");\n    this.transports = [];\n    this._transportsByName = {};\n    opts.transports.forEach(t => {\n      const transportName = t.prototype.name;\n      this.transports.push(transportName);\n      this._transportsByName[transportName] = t;\n    });\n    this.opts = Object.assign({\n      path: \"/engine.io\",\n      agent: false,\n      withCredentials: false,\n      upgrade: true,\n      timestampParam: \"t\",\n      rememberUpgrade: false,\n      addTrailingSlash: true,\n      rejectUnauthorized: true,\n      perMessageDeflate: {\n        threshold: 1024\n      },\n      transportOptions: {},\n      closeOnBeforeunload: false\n    }, opts);\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + (this.opts.addTrailingSlash ? \"/\" : \"\");\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = decode(this.opts.query);\n    }\n    if (withEventListeners) {\n      if (this.opts.closeOnBeforeunload) {\n        // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n        // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n        // closed/reloaded)\n        this._beforeunloadEventListener = () => {\n          if (this.transport) {\n            // silently close the transport\n            this.transport.removeAllListeners();\n            this.transport.close();\n          }\n        };\n        addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n      }\n      if (this.hostname !== \"localhost\") {\n        this._offlineEventListener = () => {\n          this._onClose(\"transport close\", {\n            description: \"network connection lost\"\n          });\n        };\n        OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n      }\n    }\n    if (this.opts.withCredentials) {\n      this._cookieJar = createCookieJar();\n    }\n    this._open();\n  }\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} name - transport name\n   * @return {Transport}\n   * @private\n   */\n  createTransport(name) {\n    const query = Object.assign({}, this.opts.query);\n    // append engine.io protocol identifier\n    query.EIO = protocol;\n    // transport name\n    query.transport = name;\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n    const opts = Object.assign({}, this.opts, {\n      query,\n      socket: this,\n      hostname: this.hostname,\n      secure: this.secure,\n      port: this.port\n    }, this.opts.transportOptions[name]);\n    return new this._transportsByName[name](opts);\n  }\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @private\n   */\n  _open() {\n    if (this.transports.length === 0) {\n      // Emit error on next tick so it can be listened to\n      this.setTimeoutFn(() => {\n        this.emitReserved(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    }\n    const transportName = this.opts.rememberUpgrade && SocketWithoutUpgrade.priorWebsocketSuccess && this.transports.indexOf(\"websocket\") !== -1 ? \"websocket\" : this.transports[0];\n    this.readyState = \"opening\";\n    const transport = this.createTransport(transportName);\n    transport.open();\n    this.setTransport(transport);\n  }\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @private\n   */\n  setTransport(transport) {\n    if (this.transport) {\n      this.transport.removeAllListeners();\n    }\n    // set up transport\n    this.transport = transport;\n    // set up transport listeners\n    transport.on(\"drain\", this._onDrain.bind(this)).on(\"packet\", this._onPacket.bind(this)).on(\"error\", this._onError.bind(this)).on(\"close\", reason => this._onClose(\"transport close\", reason));\n  }\n  /**\n   * Called when connection is deemed open.\n   *\n   * @private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emitReserved(\"open\");\n    this.flush();\n  }\n  /**\n   * Handles a packet.\n   *\n   * @private\n   */\n  _onPacket(packet) {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n      this.emitReserved(\"packet\", packet);\n      // Socket is live - any packet counts\n      this.emitReserved(\"heartbeat\");\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n        case \"ping\":\n          this._sendPacket(\"pong\");\n          this.emitReserved(\"ping\");\n          this.emitReserved(\"pong\");\n          this._resetPingTimeout();\n          break;\n        case \"error\":\n          const err = new Error(\"server error\");\n          // @ts-ignore\n          err.code = packet.data;\n          this._onError(err);\n          break;\n        case \"message\":\n          this.emitReserved(\"data\", packet.data);\n          this.emitReserved(\"message\", packet.data);\n          break;\n      }\n    } else {}\n  }\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} data - handshake obj\n   * @private\n   */\n  onHandshake(data) {\n    this.emitReserved(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this._pingInterval = data.pingInterval;\n    this._pingTimeout = data.pingTimeout;\n    this._maxPayload = data.maxPayload;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this._resetPingTimeout();\n  }\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @private\n   */\n  _resetPingTimeout() {\n    this.clearTimeoutFn(this._pingTimeoutTimer);\n    const delay = this._pingInterval + this._pingTimeout;\n    this._pingTimeoutTime = Date.now() + delay;\n    this._pingTimeoutTimer = this.setTimeoutFn(() => {\n      this._onClose(\"ping timeout\");\n    }, delay);\n    if (this.opts.autoUnref) {\n      this._pingTimeoutTimer.unref();\n    }\n  }\n  /**\n   * Called on `drain` event\n   *\n   * @private\n   */\n  _onDrain() {\n    this.writeBuffer.splice(0, this._prevBufferLen);\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this._prevBufferLen = 0;\n    if (0 === this.writeBuffer.length) {\n      this.emitReserved(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n  /**\n   * Flush write buffers.\n   *\n   * @private\n   */\n  flush() {\n    if (\"closed\" !== this.readyState && this.transport.writable && !this.upgrading && this.writeBuffer.length) {\n      const packets = this._getWritablePackets();\n      this.transport.send(packets);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this._prevBufferLen = packets.length;\n      this.emitReserved(\"flush\");\n    }\n  }\n  /**\n   * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n   * long-polling)\n   *\n   * @private\n   */\n  _getWritablePackets() {\n    const shouldCheckPayloadSize = this._maxPayload && this.transport.name === \"polling\" && this.writeBuffer.length > 1;\n    if (!shouldCheckPayloadSize) {\n      return this.writeBuffer;\n    }\n    let payloadSize = 1; // first packet type\n    for (let i = 0; i < this.writeBuffer.length; i++) {\n      const data = this.writeBuffer[i].data;\n      if (data) {\n        payloadSize += byteLength(data);\n      }\n      if (i > 0 && payloadSize > this._maxPayload) {\n        return this.writeBuffer.slice(0, i);\n      }\n      payloadSize += 2; // separator + packet type\n    }\n    return this.writeBuffer;\n  }\n  /**\n   * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n   *\n   * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n   * `write()` method then the message would not be buffered by the Socket.IO client.\n   *\n   * @return {boolean}\n   * @private\n   */\n  /* private */\n  _hasPingExpired() {\n    if (!this._pingTimeoutTime) return true;\n    const hasExpired = Date.now() > this._pingTimeoutTime;\n    if (hasExpired) {\n      this._pingTimeoutTime = 0;\n      nextTick(() => {\n        this._onClose(\"ping timeout\");\n      }, this.setTimeoutFn);\n    }\n    return hasExpired;\n  }\n  /**\n   * Sends a message.\n   *\n   * @param {String} msg - message.\n   * @param {Object} options.\n   * @param {Function} fn - callback function.\n   * @return {Socket} for chaining.\n   */\n  write(msg, options, fn) {\n    this._sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n  /**\n   * Sends a message. Alias of {@link Socket#write}.\n   *\n   * @param {String} msg - message.\n   * @param {Object} options.\n   * @param {Function} fn - callback function.\n   * @return {Socket} for chaining.\n   */\n  send(msg, options, fn) {\n    this._sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n  /**\n   * Sends a packet.\n   *\n   * @param {String} type: packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} fn - callback function.\n   * @private\n   */\n  _sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n    options = options || {};\n    options.compress = false !== options.compress;\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emitReserved(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n  /**\n   * Closes the connection.\n   */\n  close() {\n    const close = () => {\n      this._onClose(\"forced close\");\n      this.transport.close();\n    };\n    const cleanupAndClose = () => {\n      this.off(\"upgrade\", cleanupAndClose);\n      this.off(\"upgradeError\", cleanupAndClose);\n      close();\n    };\n    const waitForUpgrade = () => {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      this.once(\"upgrade\", cleanupAndClose);\n      this.once(\"upgradeError\", cleanupAndClose);\n    };\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", () => {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n    return this;\n  }\n  /**\n   * Called upon transport error\n   *\n   * @private\n   */\n  _onError(err) {\n    SocketWithoutUpgrade.priorWebsocketSuccess = false;\n    if (this.opts.tryAllTransports && this.transports.length > 1 && this.readyState === \"opening\") {\n      this.transports.shift();\n      return this._open();\n    }\n    this.emitReserved(\"error\", err);\n    this._onClose(\"transport error\", err);\n  }\n  /**\n   * Called upon transport close.\n   *\n   * @private\n   */\n  _onClose(reason, description) {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n      // clear timers\n      this.clearTimeoutFn(this._pingTimeoutTimer);\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n      // ensure transport won't stay open\n      this.transport.close();\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n      if (withEventListeners) {\n        if (this._beforeunloadEventListener) {\n          removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n        }\n        if (this._offlineEventListener) {\n          const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n          if (i !== -1) {\n            OFFLINE_EVENT_LISTENERS.splice(i, 1);\n          }\n        }\n      }\n      // set ready state\n      this.readyState = \"closed\";\n      // clear session id\n      this.id = null;\n      // emit close event\n      this.emitReserved(\"close\", reason, description);\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      this.writeBuffer = [];\n      this._prevBufferLen = 0;\n    }\n  }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n  constructor() {\n    super(...arguments);\n    this._upgrades = [];\n  }\n  onOpen() {\n    super.onOpen();\n    if (\"open\" === this.readyState && this.opts.upgrade) {\n      for (let i = 0; i < this._upgrades.length; i++) {\n        this._probe(this._upgrades[i]);\n      }\n    }\n  }\n  /**\n   * Probes a transport.\n   *\n   * @param {String} name - transport name\n   * @private\n   */\n  _probe(name) {\n    let transport = this.createTransport(name);\n    let failed = false;\n    SocketWithoutUpgrade.priorWebsocketSuccess = false;\n    const onTransportOpen = () => {\n      if (failed) return;\n      transport.send([{\n        type: \"ping\",\n        data: \"probe\"\n      }]);\n      transport.once(\"packet\", msg => {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n          this.upgrading = true;\n          this.emitReserved(\"upgrading\", transport);\n          if (!transport) return;\n          SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === transport.name;\n          this.transport.pause(() => {\n            if (failed) return;\n            if (\"closed\" === this.readyState) return;\n            cleanup();\n            this.setTransport(transport);\n            transport.send([{\n              type: \"upgrade\"\n            }]);\n            this.emitReserved(\"upgrade\", transport);\n            transport = null;\n            this.upgrading = false;\n            this.flush();\n          });\n        } else {\n          const err = new Error(\"probe error\");\n          // @ts-ignore\n          err.transport = transport.name;\n          this.emitReserved(\"upgradeError\", err);\n        }\n      });\n    };\n    function freezeTransport() {\n      if (failed) return;\n      // Any callback called by transport should be ignored since now\n      failed = true;\n      cleanup();\n      transport.close();\n      transport = null;\n    }\n    // Handle any error that happens while probing\n    const onerror = err => {\n      const error = new Error(\"probe error: \" + err);\n      // @ts-ignore\n      error.transport = transport.name;\n      freezeTransport();\n      this.emitReserved(\"upgradeError\", error);\n    };\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n        freezeTransport();\n      }\n    }\n    // Remove all listeners on the transport and on self\n    const cleanup = () => {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      this.off(\"close\", onclose);\n      this.off(\"upgrading\", onupgrade);\n    };\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n    if (this._upgrades.indexOf(\"webtransport\") !== -1 && name !== \"webtransport\") {\n      // favor WebTransport\n      this.setTimeoutFn(() => {\n        if (!failed) {\n          transport.open();\n        }\n      }, 200);\n    } else {\n      transport.open();\n    }\n  }\n  onHandshake(data) {\n    this._upgrades = this._filterUpgrades(data.upgrades);\n    super.onHandshake(data);\n  }\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} upgrades - server upgrades\n   * @private\n   */\n  _filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    for (let i = 0; i < upgrades.length; i++) {\n      if (~this.transports.indexOf(upgrades[i])) filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n  constructor(uri, opts = {}) {\n    const o = typeof uri === \"object\" ? uri : opts;\n    if (!o.transports || o.transports && typeof o.transports[0] === \"string\") {\n      o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"]).map(transportName => DEFAULT_TRANSPORTS[transportName]).filter(t => !!t);\n    }\n    super(uri, o);\n  }\n}", "map": {"version": 3, "names": ["transports", "DEFAULT_TRANSPORTS", "installTimerFunctions", "byteLength", "decode", "parse", "Emitter", "protocol", "createCookieJar", "defaultBinaryType", "nextTick", "withEventListeners", "addEventListener", "removeEventListener", "OFFLINE_EVENT_LISTENERS", "for<PERSON>ach", "listener", "SocketWithoutUpgrade", "constructor", "uri", "opts", "binaryType", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "parsed<PERSON><PERSON>", "hostname", "host", "secure", "port", "query", "location", "_transportsByName", "t", "transportName", "prototype", "name", "push", "Object", "assign", "path", "agent", "withCredentials", "upgrade", "timestampParam", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "replace", "_beforeunloadEventListener", "transport", "removeAllListeners", "close", "_offlineEventListener", "_onClose", "description", "_cookieJar", "_open", "createTransport", "EIO", "id", "sid", "socket", "length", "setTimeoutFn", "emit<PERSON><PERSON><PERSON><PERSON>", "priorWebsocketSuccess", "indexOf", "readyState", "open", "setTransport", "on", "_onDrain", "bind", "_onPacket", "_onError", "reason", "onOpen", "flush", "packet", "type", "onHandshake", "JSON", "data", "_sendPacket", "_resetPingTimeout", "err", "Error", "code", "pingInterval", "pingTimeout", "maxPayload", "clearTimeoutFn", "_pingTimeoutTimer", "delay", "Date", "now", "autoUnref", "unref", "splice", "writable", "upgrading", "packets", "_getWritablePackets", "send", "shouldCheckPayloadSize", "payloadSize", "i", "slice", "_hasPingExpired", "hasExpired", "write", "msg", "options", "fn", "undefined", "compress", "once", "cleanupAndClose", "off", "waitForUpgrade", "tryAllTransports", "shift", "SocketWithUpgrade", "arguments", "_upgrades", "_probe", "failed", "onTransportOpen", "pause", "cleanup", "freezeTransport", "onerror", "error", "onTransportClose", "onclose", "onupgrade", "to", "removeListener", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "o", "map", "filter"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/engine.io-client/build/esm/socket.js"], "sourcesContent": ["import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,kBAAkB,QAAQ,uBAAuB;AACxE,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,WAAW;AAC7D,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,QAAQ,QAAS,mBAAmB;AACjF,MAAMC,kBAAkB,GAAG,OAAOC,gBAAgB,KAAK,UAAU,IAC7D,OAAOC,mBAAmB,KAAK,UAAU;AAC7C,MAAMC,uBAAuB,GAAG,EAAE;AAClC,IAAIH,kBAAkB,EAAE;EACpB;EACA;EACAC,gBAAgB,CAAC,SAAS,EAAE,MAAM;IAC9BE,uBAAuB,CAACC,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;EAC7D,CAAC,EAAE,KAAK,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,SAASX,OAAO,CAAC;EAC9C;AACJ;AACA;AACA;AACA;AACA;EACIY,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,GAAGZ,iBAAiB;IACnC,IAAI,CAACa,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAGC,QAAQ;IAChC,IAAIT,GAAG,IAAI,QAAQ,KAAK,OAAOA,GAAG,EAAE;MAChCC,IAAI,GAAGD,GAAG;MACVA,GAAG,GAAG,IAAI;IACd;IACA,IAAIA,GAAG,EAAE;MACL,MAAMU,SAAS,GAAGxB,KAAK,CAACc,GAAG,CAAC;MAC5BC,IAAI,CAACU,QAAQ,GAAGD,SAAS,CAACE,IAAI;MAC9BX,IAAI,CAACY,MAAM,GACPH,SAAS,CAACtB,QAAQ,KAAK,OAAO,IAAIsB,SAAS,CAACtB,QAAQ,KAAK,KAAK;MAClEa,IAAI,CAACa,IAAI,GAAGJ,SAAS,CAACI,IAAI;MAC1B,IAAIJ,SAAS,CAACK,KAAK,EACfd,IAAI,CAACc,KAAK,GAAGL,SAAS,CAACK,KAAK;IACpC,CAAC,MACI,IAAId,IAAI,CAACW,IAAI,EAAE;MAChBX,IAAI,CAACU,QAAQ,GAAGzB,KAAK,CAACe,IAAI,CAACW,IAAI,CAAC,CAACA,IAAI;IACzC;IACA7B,qBAAqB,CAAC,IAAI,EAAEkB,IAAI,CAAC;IACjC,IAAI,CAACY,MAAM,GACP,IAAI,IAAIZ,IAAI,CAACY,MAAM,GACbZ,IAAI,CAACY,MAAM,GACX,OAAOG,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAKA,QAAQ,CAAC5B,QAAQ;IAC3E,IAAIa,IAAI,CAACU,QAAQ,IAAI,CAACV,IAAI,CAACa,IAAI,EAAE;MAC7B;MACAb,IAAI,CAACa,IAAI,GAAG,IAAI,CAACD,MAAM,GAAG,KAAK,GAAG,IAAI;IAC1C;IACA,IAAI,CAACF,QAAQ,GACTV,IAAI,CAACU,QAAQ,KACR,OAAOK,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACL,QAAQ,GAAG,WAAW,CAAC;IAC3E,IAAI,CAACG,IAAI,GACLb,IAAI,CAACa,IAAI,KACJ,OAAOE,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACF,IAAI,GAC3CE,QAAQ,CAACF,IAAI,GACb,IAAI,CAACD,MAAM,GACP,KAAK,GACL,IAAI,CAAC;IACvB,IAAI,CAAChC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoC,iBAAiB,GAAG,CAAC,CAAC;IAC3BhB,IAAI,CAACpB,UAAU,CAACe,OAAO,CAAEsB,CAAC,IAAK;MAC3B,MAAMC,aAAa,GAAGD,CAAC,CAACE,SAAS,CAACC,IAAI;MACtC,IAAI,CAACxC,UAAU,CAACyC,IAAI,CAACH,aAAa,CAAC;MACnC,IAAI,CAACF,iBAAiB,CAACE,aAAa,CAAC,GAAGD,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI,CAACjB,IAAI,GAAGsB,MAAM,CAACC,MAAM,CAAC;MACtBC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,KAAK;MACZC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,GAAG;MACnBC,eAAe,EAAE,KAAK;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,iBAAiB,EAAE;QACfC,SAAS,EAAE;MACf,CAAC;MACDC,gBAAgB,EAAE,CAAC,CAAC;MACpBC,mBAAmB,EAAE;IACzB,CAAC,EAAEnC,IAAI,CAAC;IACR,IAAI,CAACA,IAAI,CAACwB,IAAI,GACV,IAAI,CAACxB,IAAI,CAACwB,IAAI,CAACY,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC5B,IAAI,CAACpC,IAAI,CAAC8B,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC;IAC/C,IAAI,OAAO,IAAI,CAAC9B,IAAI,CAACc,KAAK,KAAK,QAAQ,EAAE;MACrC,IAAI,CAACd,IAAI,CAACc,KAAK,GAAG9B,MAAM,CAAC,IAAI,CAACgB,IAAI,CAACc,KAAK,CAAC;IAC7C;IACA,IAAIvB,kBAAkB,EAAE;MACpB,IAAI,IAAI,CAACS,IAAI,CAACmC,mBAAmB,EAAE;QAC/B;QACA;QACA;QACA,IAAI,CAACE,0BAA0B,GAAG,MAAM;UACpC,IAAI,IAAI,CAACC,SAAS,EAAE;YAChB;YACA,IAAI,CAACA,SAAS,CAACC,kBAAkB,CAAC,CAAC;YACnC,IAAI,CAACD,SAAS,CAACE,KAAK,CAAC,CAAC;UAC1B;QACJ,CAAC;QACDhD,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC6C,0BAA0B,EAAE,KAAK,CAAC;MAC5E;MACA,IAAI,IAAI,CAAC3B,QAAQ,KAAK,WAAW,EAAE;QAC/B,IAAI,CAAC+B,qBAAqB,GAAG,MAAM;UAC/B,IAAI,CAACC,QAAQ,CAAC,iBAAiB,EAAE;YAC7BC,WAAW,EAAE;UACjB,CAAC,CAAC;QACN,CAAC;QACDjD,uBAAuB,CAAC2B,IAAI,CAAC,IAAI,CAACoB,qBAAqB,CAAC;MAC5D;IACJ;IACA,IAAI,IAAI,CAACzC,IAAI,CAAC0B,eAAe,EAAE;MAC3B,IAAI,CAACkB,UAAU,GAAGxD,eAAe,CAAC,CAAC;IACvC;IACA,IAAI,CAACyD,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAAC1B,IAAI,EAAE;IAClB,MAAMN,KAAK,GAAGQ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACvB,IAAI,CAACc,KAAK,CAAC;IAChD;IACAA,KAAK,CAACiC,GAAG,GAAG5D,QAAQ;IACpB;IACA2B,KAAK,CAACwB,SAAS,GAAGlB,IAAI;IACtB;IACA,IAAI,IAAI,CAAC4B,EAAE,EACPlC,KAAK,CAACmC,GAAG,GAAG,IAAI,CAACD,EAAE;IACvB,MAAMhD,IAAI,GAAGsB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACvB,IAAI,EAAE;MACtCc,KAAK;MACLoC,MAAM,EAAE,IAAI;MACZxC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBE,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,IAAI,EAAE,IAAI,CAACA;IACf,CAAC,EAAE,IAAI,CAACb,IAAI,CAACkC,gBAAgB,CAACd,IAAI,CAAC,CAAC;IACpC,OAAO,IAAI,IAAI,CAACJ,iBAAiB,CAACI,IAAI,CAAC,CAACpB,IAAI,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;EACI6C,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACjE,UAAU,CAACuE,MAAM,KAAK,CAAC,EAAE;MAC9B;MACA,IAAI,CAACC,YAAY,CAAC,MAAM;QACpB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE,yBAAyB,CAAC;MACzD,CAAC,EAAE,CAAC,CAAC;MACL;IACJ;IACA,MAAMnC,aAAa,GAAG,IAAI,CAAClB,IAAI,CAAC6B,eAAe,IAC3ChC,oBAAoB,CAACyD,qBAAqB,IAC1C,IAAI,CAAC1E,UAAU,CAAC2E,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GACzC,WAAW,GACX,IAAI,CAAC3E,UAAU,CAAC,CAAC,CAAC;IACxB,IAAI,CAAC4E,UAAU,GAAG,SAAS;IAC3B,MAAMlB,SAAS,GAAG,IAAI,CAACQ,eAAe,CAAC5B,aAAa,CAAC;IACrDoB,SAAS,CAACmB,IAAI,CAAC,CAAC;IAChB,IAAI,CAACC,YAAY,CAACpB,SAAS,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIoB,YAAYA,CAACpB,SAAS,EAAE;IACpB,IAAI,IAAI,CAACA,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACC,kBAAkB,CAAC,CAAC;IACvC;IACA;IACA,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B;IACAA,SAAS,CACJqB,EAAE,CAAC,OAAO,EAAE,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CACrCF,EAAE,CAAC,QAAQ,EAAE,IAAI,CAACG,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,CACvCF,EAAE,CAAC,OAAO,EAAE,IAAI,CAACI,QAAQ,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC,CACrCF,EAAE,CAAC,OAAO,EAAGK,MAAM,IAAK,IAAI,CAACtB,QAAQ,CAAC,iBAAiB,EAAEsB,MAAM,CAAC,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACT,UAAU,GAAG,MAAM;IACxB3D,oBAAoB,CAACyD,qBAAqB,GACtC,WAAW,KAAK,IAAI,CAAChB,SAAS,CAAClB,IAAI;IACvC,IAAI,CAACiC,YAAY,CAAC,MAAM,CAAC;IACzB,IAAI,CAACa,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;EACIJ,SAASA,CAACK,MAAM,EAAE;IACd,IAAI,SAAS,KAAK,IAAI,CAACX,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;MAC/B,IAAI,CAACH,YAAY,CAAC,QAAQ,EAAEc,MAAM,CAAC;MACnC;MACA,IAAI,CAACd,YAAY,CAAC,WAAW,CAAC;MAC9B,QAAQc,MAAM,CAACC,IAAI;QACf,KAAK,MAAM;UACP,IAAI,CAACC,WAAW,CAACC,IAAI,CAACrF,KAAK,CAACkF,MAAM,CAACI,IAAI,CAAC,CAAC;UACzC;QACJ,KAAK,MAAM;UACP,IAAI,CAACC,WAAW,CAAC,MAAM,CAAC;UACxB,IAAI,CAACnB,YAAY,CAAC,MAAM,CAAC;UACzB,IAAI,CAACA,YAAY,CAAC,MAAM,CAAC;UACzB,IAAI,CAACoB,iBAAiB,CAAC,CAAC;UACxB;QACJ,KAAK,OAAO;UACR,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,cAAc,CAAC;UACrC;UACAD,GAAG,CAACE,IAAI,GAAGT,MAAM,CAACI,IAAI;UACtB,IAAI,CAACR,QAAQ,CAACW,GAAG,CAAC;UAClB;QACJ,KAAK,SAAS;UACV,IAAI,CAACrB,YAAY,CAAC,MAAM,EAAEc,MAAM,CAACI,IAAI,CAAC;UACtC,IAAI,CAAClB,YAAY,CAAC,SAAS,EAAEc,MAAM,CAACI,IAAI,CAAC;UACzC;MACR;IACJ,CAAC,MACI,CACL;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,WAAWA,CAACE,IAAI,EAAE;IACd,IAAI,CAAClB,YAAY,CAAC,WAAW,EAAEkB,IAAI,CAAC;IACpC,IAAI,CAACvB,EAAE,GAAGuB,IAAI,CAACtB,GAAG;IAClB,IAAI,CAACX,SAAS,CAACxB,KAAK,CAACmC,GAAG,GAAGsB,IAAI,CAACtB,GAAG;IACnC,IAAI,CAAC7C,aAAa,GAAGmE,IAAI,CAACM,YAAY;IACtC,IAAI,CAACxE,YAAY,GAAGkE,IAAI,CAACO,WAAW;IACpC,IAAI,CAACxE,WAAW,GAAGiE,IAAI,CAACQ,UAAU;IAClC,IAAI,CAACd,MAAM,CAAC,CAAC;IACb;IACA,IAAI,QAAQ,KAAK,IAAI,CAACT,UAAU,EAC5B;IACJ,IAAI,CAACiB,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACO,cAAc,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAAC9E,aAAa,GAAG,IAAI,CAACC,YAAY;IACpD,IAAI,CAACE,gBAAgB,GAAG4E,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK;IAC1C,IAAI,CAACD,iBAAiB,GAAG,IAAI,CAAC7B,YAAY,CAAC,MAAM;MAC7C,IAAI,CAACV,QAAQ,CAAC,cAAc,CAAC;IACjC,CAAC,EAAEwC,KAAK,CAAC;IACT,IAAI,IAAI,CAAClF,IAAI,CAACqF,SAAS,EAAE;MACrB,IAAI,CAACJ,iBAAiB,CAACK,KAAK,CAAC,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI1B,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC1D,WAAW,CAACqF,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpF,cAAc,CAAC;IAC/C;IACA;IACA;IACA,IAAI,CAACA,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC,KAAK,IAAI,CAACD,WAAW,CAACiD,MAAM,EAAE;MAC/B,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC;IAC9B,CAAC,MACI;MACD,IAAI,CAACa,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ,IAAI,QAAQ,KAAK,IAAI,CAACV,UAAU,IAC5B,IAAI,CAAClB,SAAS,CAACkD,QAAQ,IACvB,CAAC,IAAI,CAACC,SAAS,IACf,IAAI,CAACvF,WAAW,CAACiD,MAAM,EAAE;MACzB,MAAMuC,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1C,IAAI,CAACrD,SAAS,CAACsD,IAAI,CAACF,OAAO,CAAC;MAC5B;MACA;MACA,IAAI,CAACvF,cAAc,GAAGuF,OAAO,CAACvC,MAAM;MACpC,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC;IAC9B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsC,mBAAmBA,CAAA,EAAG;IAClB,MAAME,sBAAsB,GAAG,IAAI,CAACvF,WAAW,IAC3C,IAAI,CAACgC,SAAS,CAAClB,IAAI,KAAK,SAAS,IACjC,IAAI,CAAClB,WAAW,CAACiD,MAAM,GAAG,CAAC;IAC/B,IAAI,CAAC0C,sBAAsB,EAAE;MACzB,OAAO,IAAI,CAAC3F,WAAW;IAC3B;IACA,IAAI4F,WAAW,GAAG,CAAC,CAAC,CAAC;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC7F,WAAW,CAACiD,MAAM,EAAE4C,CAAC,EAAE,EAAE;MAC9C,MAAMxB,IAAI,GAAG,IAAI,CAACrE,WAAW,CAAC6F,CAAC,CAAC,CAACxB,IAAI;MACrC,IAAIA,IAAI,EAAE;QACNuB,WAAW,IAAI/G,UAAU,CAACwF,IAAI,CAAC;MACnC;MACA,IAAIwB,CAAC,GAAG,CAAC,IAAID,WAAW,GAAG,IAAI,CAACxF,WAAW,EAAE;QACzC,OAAO,IAAI,CAACJ,WAAW,CAAC8F,KAAK,CAAC,CAAC,EAAED,CAAC,CAAC;MACvC;MACAD,WAAW,IAAI,CAAC,CAAC,CAAC;IACtB;IACA,OAAO,IAAI,CAAC5F,WAAW;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EAAc+F,eAAeA,CAAA,EAAG;IAC5B,IAAI,CAAC,IAAI,CAAC1F,gBAAgB,EACtB,OAAO,IAAI;IACf,MAAM2F,UAAU,GAAGf,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC7E,gBAAgB;IACrD,IAAI2F,UAAU,EAAE;MACZ,IAAI,CAAC3F,gBAAgB,GAAG,CAAC;MACzBjB,QAAQ,CAAC,MAAM;QACX,IAAI,CAACoD,QAAQ,CAAC,cAAc,CAAC;MACjC,CAAC,EAAE,IAAI,CAACU,YAAY,CAAC;IACzB;IACA,OAAO8C,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACC,GAAG,EAAEC,OAAO,EAAEC,EAAE,EAAE;IACpB,IAAI,CAAC9B,WAAW,CAAC,SAAS,EAAE4B,GAAG,EAAEC,OAAO,EAAEC,EAAE,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIV,IAAIA,CAACQ,GAAG,EAAEC,OAAO,EAAEC,EAAE,EAAE;IACnB,IAAI,CAAC9B,WAAW,CAAC,SAAS,EAAE4B,GAAG,EAAEC,OAAO,EAAEC,EAAE,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9B,WAAWA,CAACJ,IAAI,EAAEG,IAAI,EAAE8B,OAAO,EAAEC,EAAE,EAAE;IACjC,IAAI,UAAU,KAAK,OAAO/B,IAAI,EAAE;MAC5B+B,EAAE,GAAG/B,IAAI;MACTA,IAAI,GAAGgC,SAAS;IACpB;IACA,IAAI,UAAU,KAAK,OAAOF,OAAO,EAAE;MAC/BC,EAAE,GAAGD,OAAO;MACZA,OAAO,GAAG,IAAI;IAClB;IACA,IAAI,SAAS,KAAK,IAAI,CAAC7C,UAAU,IAAI,QAAQ,KAAK,IAAI,CAACA,UAAU,EAAE;MAC/D;IACJ;IACA6C,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvBA,OAAO,CAACG,QAAQ,GAAG,KAAK,KAAKH,OAAO,CAACG,QAAQ;IAC7C,MAAMrC,MAAM,GAAG;MACXC,IAAI,EAAEA,IAAI;MACVG,IAAI,EAAEA,IAAI;MACV8B,OAAO,EAAEA;IACb,CAAC;IACD,IAAI,CAAChD,YAAY,CAAC,cAAc,EAAEc,MAAM,CAAC;IACzC,IAAI,CAACjE,WAAW,CAACmB,IAAI,CAAC8C,MAAM,CAAC;IAC7B,IAAImC,EAAE,EACF,IAAI,CAACG,IAAI,CAAC,OAAO,EAAEH,EAAE,CAAC;IAC1B,IAAI,CAACpC,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;EACI1B,KAAKA,CAAA,EAAG;IACJ,MAAMA,KAAK,GAAGA,CAAA,KAAM;MAChB,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC;MAC7B,IAAI,CAACJ,SAAS,CAACE,KAAK,CAAC,CAAC;IAC1B,CAAC;IACD,MAAMkE,eAAe,GAAGA,CAAA,KAAM;MAC1B,IAAI,CAACC,GAAG,CAAC,SAAS,EAAED,eAAe,CAAC;MACpC,IAAI,CAACC,GAAG,CAAC,cAAc,EAAED,eAAe,CAAC;MACzClE,KAAK,CAAC,CAAC;IACX,CAAC;IACD,MAAMoE,cAAc,GAAGA,CAAA,KAAM;MACzB;MACA,IAAI,CAACH,IAAI,CAAC,SAAS,EAAEC,eAAe,CAAC;MACrC,IAAI,CAACD,IAAI,CAAC,cAAc,EAAEC,eAAe,CAAC;IAC9C,CAAC;IACD,IAAI,SAAS,KAAK,IAAI,CAAClD,UAAU,IAAI,MAAM,KAAK,IAAI,CAACA,UAAU,EAAE;MAC7D,IAAI,CAACA,UAAU,GAAG,SAAS;MAC3B,IAAI,IAAI,CAACtD,WAAW,CAACiD,MAAM,EAAE;QACzB,IAAI,CAACsD,IAAI,CAAC,OAAO,EAAE,MAAM;UACrB,IAAI,IAAI,CAAChB,SAAS,EAAE;YAChBmB,cAAc,CAAC,CAAC;UACpB,CAAC,MACI;YACDpE,KAAK,CAAC,CAAC;UACX;QACJ,CAAC,CAAC;MACN,CAAC,MACI,IAAI,IAAI,CAACiD,SAAS,EAAE;QACrBmB,cAAc,CAAC,CAAC;MACpB,CAAC,MACI;QACDpE,KAAK,CAAC,CAAC;MACX;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIuB,QAAQA,CAACW,GAAG,EAAE;IACV7E,oBAAoB,CAACyD,qBAAqB,GAAG,KAAK;IAClD,IAAI,IAAI,CAACtD,IAAI,CAAC6G,gBAAgB,IAC1B,IAAI,CAACjI,UAAU,CAACuE,MAAM,GAAG,CAAC,IAC1B,IAAI,CAACK,UAAU,KAAK,SAAS,EAAE;MAC/B,IAAI,CAAC5E,UAAU,CAACkI,KAAK,CAAC,CAAC;MACvB,OAAO,IAAI,CAACjE,KAAK,CAAC,CAAC;IACvB;IACA,IAAI,CAACQ,YAAY,CAAC,OAAO,EAAEqB,GAAG,CAAC;IAC/B,IAAI,CAAChC,QAAQ,CAAC,iBAAiB,EAAEgC,GAAG,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACIhC,QAAQA,CAACsB,MAAM,EAAErB,WAAW,EAAE;IAC1B,IAAI,SAAS,KAAK,IAAI,CAACa,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;MAC/B;MACA,IAAI,CAACwB,cAAc,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC3C;MACA,IAAI,CAAC3C,SAAS,CAACC,kBAAkB,CAAC,OAAO,CAAC;MAC1C;MACA,IAAI,CAACD,SAAS,CAACE,KAAK,CAAC,CAAC;MACtB;MACA,IAAI,CAACF,SAAS,CAACC,kBAAkB,CAAC,CAAC;MACnC,IAAIhD,kBAAkB,EAAE;QACpB,IAAI,IAAI,CAAC8C,0BAA0B,EAAE;UACjC5C,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC4C,0BAA0B,EAAE,KAAK,CAAC;QAC/E;QACA,IAAI,IAAI,CAACI,qBAAqB,EAAE;UAC5B,MAAMsD,CAAC,GAAGrG,uBAAuB,CAAC6D,OAAO,CAAC,IAAI,CAACd,qBAAqB,CAAC;UACrE,IAAIsD,CAAC,KAAK,CAAC,CAAC,EAAE;YACVrG,uBAAuB,CAAC6F,MAAM,CAACQ,CAAC,EAAE,CAAC,CAAC;UACxC;QACJ;MACJ;MACA;MACA,IAAI,CAACvC,UAAU,GAAG,QAAQ;MAC1B;MACA,IAAI,CAACR,EAAE,GAAG,IAAI;MACd;MACA,IAAI,CAACK,YAAY,CAAC,OAAO,EAAEW,MAAM,EAAErB,WAAW,CAAC;MAC/C;MACA;MACA,IAAI,CAACzC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,cAAc,GAAG,CAAC;IAC3B;EACJ;AACJ;AACAN,oBAAoB,CAACV,QAAQ,GAAGA,QAAQ;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4H,iBAAiB,SAASlH,oBAAoB,CAAC;EACxDC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGkH,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACAhD,MAAMA,CAAA,EAAG;IACL,KAAK,CAACA,MAAM,CAAC,CAAC;IACd,IAAI,MAAM,KAAK,IAAI,CAACT,UAAU,IAAI,IAAI,CAACxD,IAAI,CAAC2B,OAAO,EAAE;MACjD,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACkB,SAAS,CAAC9D,MAAM,EAAE4C,CAAC,EAAE,EAAE;QAC5C,IAAI,CAACmB,MAAM,CAAC,IAAI,CAACD,SAAS,CAAClB,CAAC,CAAC,CAAC;MAClC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACImB,MAAMA,CAAC9F,IAAI,EAAE;IACT,IAAIkB,SAAS,GAAG,IAAI,CAACQ,eAAe,CAAC1B,IAAI,CAAC;IAC1C,IAAI+F,MAAM,GAAG,KAAK;IAClBtH,oBAAoB,CAACyD,qBAAqB,GAAG,KAAK;IAClD,MAAM8D,eAAe,GAAGA,CAAA,KAAM;MAC1B,IAAID,MAAM,EACN;MACJ7E,SAAS,CAACsD,IAAI,CAAC,CAAC;QAAExB,IAAI,EAAE,MAAM;QAAEG,IAAI,EAAE;MAAQ,CAAC,CAAC,CAAC;MACjDjC,SAAS,CAACmE,IAAI,CAAC,QAAQ,EAAGL,GAAG,IAAK;QAC9B,IAAIe,MAAM,EACN;QACJ,IAAI,MAAM,KAAKf,GAAG,CAAChC,IAAI,IAAI,OAAO,KAAKgC,GAAG,CAAC7B,IAAI,EAAE;UAC7C,IAAI,CAACkB,SAAS,GAAG,IAAI;UACrB,IAAI,CAACpC,YAAY,CAAC,WAAW,EAAEf,SAAS,CAAC;UACzC,IAAI,CAACA,SAAS,EACV;UACJzC,oBAAoB,CAACyD,qBAAqB,GACtC,WAAW,KAAKhB,SAAS,CAAClB,IAAI;UAClC,IAAI,CAACkB,SAAS,CAAC+E,KAAK,CAAC,MAAM;YACvB,IAAIF,MAAM,EACN;YACJ,IAAI,QAAQ,KAAK,IAAI,CAAC3D,UAAU,EAC5B;YACJ8D,OAAO,CAAC,CAAC;YACT,IAAI,CAAC5D,YAAY,CAACpB,SAAS,CAAC;YAC5BA,SAAS,CAACsD,IAAI,CAAC,CAAC;cAAExB,IAAI,EAAE;YAAU,CAAC,CAAC,CAAC;YACrC,IAAI,CAACf,YAAY,CAAC,SAAS,EAAEf,SAAS,CAAC;YACvCA,SAAS,GAAG,IAAI;YAChB,IAAI,CAACmD,SAAS,GAAG,KAAK;YACtB,IAAI,CAACvB,KAAK,CAAC,CAAC;UAChB,CAAC,CAAC;QACN,CAAC,MACI;UACD,MAAMQ,GAAG,GAAG,IAAIC,KAAK,CAAC,aAAa,CAAC;UACpC;UACAD,GAAG,CAACpC,SAAS,GAAGA,SAAS,CAAClB,IAAI;UAC9B,IAAI,CAACiC,YAAY,CAAC,cAAc,EAAEqB,GAAG,CAAC;QAC1C;MACJ,CAAC,CAAC;IACN,CAAC;IACD,SAAS6C,eAAeA,CAAA,EAAG;MACvB,IAAIJ,MAAM,EACN;MACJ;MACAA,MAAM,GAAG,IAAI;MACbG,OAAO,CAAC,CAAC;MACThF,SAAS,CAACE,KAAK,CAAC,CAAC;MACjBF,SAAS,GAAG,IAAI;IACpB;IACA;IACA,MAAMkF,OAAO,GAAI9C,GAAG,IAAK;MACrB,MAAM+C,KAAK,GAAG,IAAI9C,KAAK,CAAC,eAAe,GAAGD,GAAG,CAAC;MAC9C;MACA+C,KAAK,CAACnF,SAAS,GAAGA,SAAS,CAAClB,IAAI;MAChCmG,eAAe,CAAC,CAAC;MACjB,IAAI,CAAClE,YAAY,CAAC,cAAc,EAAEoE,KAAK,CAAC;IAC5C,CAAC;IACD,SAASC,gBAAgBA,CAAA,EAAG;MACxBF,OAAO,CAAC,kBAAkB,CAAC;IAC/B;IACA;IACA,SAASG,OAAOA,CAAA,EAAG;MACfH,OAAO,CAAC,eAAe,CAAC;IAC5B;IACA;IACA,SAASI,SAASA,CAACC,EAAE,EAAE;MACnB,IAAIvF,SAAS,IAAIuF,EAAE,CAACzG,IAAI,KAAKkB,SAAS,CAAClB,IAAI,EAAE;QACzCmG,eAAe,CAAC,CAAC;MACrB;IACJ;IACA;IACA,MAAMD,OAAO,GAAGA,CAAA,KAAM;MAClBhF,SAAS,CAACwF,cAAc,CAAC,MAAM,EAAEV,eAAe,CAAC;MACjD9E,SAAS,CAACwF,cAAc,CAAC,OAAO,EAAEN,OAAO,CAAC;MAC1ClF,SAAS,CAACwF,cAAc,CAAC,OAAO,EAAEJ,gBAAgB,CAAC;MACnD,IAAI,CAACf,GAAG,CAAC,OAAO,EAAEgB,OAAO,CAAC;MAC1B,IAAI,CAAChB,GAAG,CAAC,WAAW,EAAEiB,SAAS,CAAC;IACpC,CAAC;IACDtF,SAAS,CAACmE,IAAI,CAAC,MAAM,EAAEW,eAAe,CAAC;IACvC9E,SAAS,CAACmE,IAAI,CAAC,OAAO,EAAEe,OAAO,CAAC;IAChClF,SAAS,CAACmE,IAAI,CAAC,OAAO,EAAEiB,gBAAgB,CAAC;IACzC,IAAI,CAACjB,IAAI,CAAC,OAAO,EAAEkB,OAAO,CAAC;IAC3B,IAAI,CAAClB,IAAI,CAAC,WAAW,EAAEmB,SAAS,CAAC;IACjC,IAAI,IAAI,CAACX,SAAS,CAAC1D,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAC7CnC,IAAI,KAAK,cAAc,EAAE;MACzB;MACA,IAAI,CAACgC,YAAY,CAAC,MAAM;QACpB,IAAI,CAAC+D,MAAM,EAAE;UACT7E,SAAS,CAACmB,IAAI,CAAC,CAAC;QACpB;MACJ,CAAC,EAAE,GAAG,CAAC;IACX,CAAC,MACI;MACDnB,SAAS,CAACmB,IAAI,CAAC,CAAC;IACpB;EACJ;EACAY,WAAWA,CAACE,IAAI,EAAE;IACd,IAAI,CAAC0C,SAAS,GAAG,IAAI,CAACc,eAAe,CAACxD,IAAI,CAACyD,QAAQ,CAAC;IACpD,KAAK,CAAC3D,WAAW,CAACE,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwD,eAAeA,CAACC,QAAQ,EAAE;IACtB,MAAMC,gBAAgB,GAAG,EAAE;IAC3B,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,QAAQ,CAAC7E,MAAM,EAAE4C,CAAC,EAAE,EAAE;MACtC,IAAI,CAAC,IAAI,CAACnH,UAAU,CAAC2E,OAAO,CAACyE,QAAQ,CAACjC,CAAC,CAAC,CAAC,EACrCkC,gBAAgB,CAAC5G,IAAI,CAAC2G,QAAQ,CAACjC,CAAC,CAAC,CAAC;IAC1C;IACA,OAAOkC,gBAAgB;EAC3B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,SAASnB,iBAAiB,CAAC;EAC1CjH,WAAWA,CAACC,GAAG,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IACxB,MAAMmI,CAAC,GAAG,OAAOpI,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGC,IAAI;IAC9C,IAAI,CAACmI,CAAC,CAACvJ,UAAU,IACZuJ,CAAC,CAACvJ,UAAU,IAAI,OAAOuJ,CAAC,CAACvJ,UAAU,CAAC,CAAC,CAAC,KAAK,QAAS,EAAE;MACvDuJ,CAAC,CAACvJ,UAAU,GAAG,CAACuJ,CAAC,CAACvJ,UAAU,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EACnEwJ,GAAG,CAAElH,aAAa,IAAKrC,kBAAkB,CAACqC,aAAa,CAAC,CAAC,CACzDmH,MAAM,CAAEpH,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;IAC3B;IACA,KAAK,CAAClB,GAAG,EAAEoI,CAAC,CAAC;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}