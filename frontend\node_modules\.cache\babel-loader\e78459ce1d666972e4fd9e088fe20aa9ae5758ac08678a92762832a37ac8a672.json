{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction debounce(func, wait = 0, options = {}) {\n  if (typeof options !== 'object') {\n    options = {};\n  }\n  let pendingArgs = null;\n  let pendingThis = null;\n  let lastCallTime = null;\n  let debounceStartTime = 0;\n  let timeoutId = null;\n  let lastResult;\n  const {\n    leading = false,\n    trailing = true,\n    maxWait\n  } = options;\n  const hasMaxWait = 'maxWait' in options;\n  const maxWaitMs = hasMaxWait ? Math.max(Number(maxWait) || 0, wait) : 0;\n  const invoke = time => {\n    if (pendingArgs !== null) {\n      lastResult = func.apply(pendingThis, pendingArgs);\n    }\n    pendingArgs = pendingThis = null;\n    debounceStartTime = time;\n    return lastResult;\n  };\n  const handleLeading = time => {\n    debounceStartTime = time;\n    timeoutId = setTimeout(handleTimeout, wait);\n    if (leading && pendingArgs !== null) {\n      return invoke(time);\n    }\n    return lastResult;\n  };\n  const handleTrailing = time => {\n    timeoutId = null;\n    if (trailing && pendingArgs !== null) {\n      return invoke(time);\n    }\n    return lastResult;\n  };\n  const checkCanInvoke = time => {\n    if (lastCallTime === null) {\n      return true;\n    }\n    const timeSinceLastCall = time - lastCallTime;\n    const hasDebounceDelayPassed = timeSinceLastCall >= wait || timeSinceLastCall < 0;\n    const hasMaxWaitPassed = hasMaxWait && time - debounceStartTime >= maxWaitMs;\n    return hasDebounceDelayPassed || hasMaxWaitPassed;\n  };\n  const calculateRemainingWait = time => {\n    const timeSinceLastCall = lastCallTime === null ? 0 : time - lastCallTime;\n    const remainingDebounceTime = wait - timeSinceLastCall;\n    const remainingMaxWaitTime = maxWaitMs - (time - debounceStartTime);\n    return hasMaxWait ? Math.min(remainingDebounceTime, remainingMaxWaitTime) : remainingDebounceTime;\n  };\n  const handleTimeout = () => {\n    const currentTime = Date.now();\n    if (checkCanInvoke(currentTime)) {\n      return handleTrailing(currentTime);\n    }\n    timeoutId = setTimeout(handleTimeout, calculateRemainingWait(currentTime));\n  };\n  const debouncedFunction = function (...args) {\n    const currentTime = Date.now();\n    const canInvoke = checkCanInvoke(currentTime);\n    pendingArgs = args;\n    pendingThis = this;\n    lastCallTime = currentTime;\n    if (canInvoke) {\n      if (timeoutId === null) {\n        return handleLeading(currentTime);\n      }\n      if (hasMaxWait) {\n        clearTimeout(timeoutId);\n        timeoutId = setTimeout(handleTimeout, wait);\n        return invoke(currentTime);\n      }\n    }\n    if (timeoutId === null) {\n      timeoutId = setTimeout(handleTimeout, wait);\n    }\n    return lastResult;\n  };\n  debouncedFunction.cancel = () => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    debounceStartTime = 0;\n    lastCallTime = pendingArgs = pendingThis = timeoutId = null;\n  };\n  debouncedFunction.flush = () => {\n    return timeoutId === null ? lastResult : handleTrailing(Date.now());\n  };\n  return debouncedFunction;\n}\nexports.debounce = debounce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce", "func", "wait", "options", "<PERSON><PERSON><PERSON>s", "pendingThis", "lastCallTime", "debounceStartTime", "timeoutId", "lastResult", "leading", "trailing", "max<PERSON><PERSON>", "hasMaxWait", "maxWait<PERSON>", "Math", "max", "Number", "invoke", "time", "apply", "handleLeading", "setTimeout", "handleTimeout", "handleTrailing", "checkCanInvoke", "timeSinceLastCall", "hasDebounce<PERSON><PERSON>yPassed", "hasMaxWaitPassed", "calculateRemainingWait", "remainingDebounceTime", "remainingMaxWaitTime", "min", "currentTime", "Date", "now", "debouncedFunction", "args", "canInvoke", "clearTimeout", "cancel", "flush"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/es-toolkit/dist/compat/function/debounce.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction debounce(func, wait = 0, options = {}) {\n    if (typeof options !== 'object') {\n        options = {};\n    }\n    let pendingArgs = null;\n    let pendingThis = null;\n    let lastCallTime = null;\n    let debounceStartTime = 0;\n    let timeoutId = null;\n    let lastResult;\n    const { leading = false, trailing = true, maxWait } = options;\n    const hasMaxWait = 'maxWait' in options;\n    const maxWaitMs = hasMaxWait ? Math.max(Number(maxWait) || 0, wait) : 0;\n    const invoke = (time) => {\n        if (pendingArgs !== null) {\n            lastResult = func.apply(pendingThis, pendingArgs);\n        }\n        pendingArgs = pendingThis = null;\n        debounceStartTime = time;\n        return lastResult;\n    };\n    const handleLeading = (time) => {\n        debounceStartTime = time;\n        timeoutId = setTimeout(handleTimeout, wait);\n        if (leading && pendingArgs !== null) {\n            return invoke(time);\n        }\n        return lastResult;\n    };\n    const handleTrailing = (time) => {\n        timeoutId = null;\n        if (trailing && pendingArgs !== null) {\n            return invoke(time);\n        }\n        return lastResult;\n    };\n    const checkCanInvoke = (time) => {\n        if (lastCallTime === null) {\n            return true;\n        }\n        const timeSinceLastCall = time - lastCallTime;\n        const hasDebounceDelayPassed = timeSinceLastCall >= wait || timeSinceLastCall < 0;\n        const hasMaxWaitPassed = hasMaxWait && time - debounceStartTime >= maxWaitMs;\n        return hasDebounceDelayPassed || hasMaxWaitPassed;\n    };\n    const calculateRemainingWait = (time) => {\n        const timeSinceLastCall = lastCallTime === null ? 0 : time - lastCallTime;\n        const remainingDebounceTime = wait - timeSinceLastCall;\n        const remainingMaxWaitTime = maxWaitMs - (time - debounceStartTime);\n        return hasMaxWait ? Math.min(remainingDebounceTime, remainingMaxWaitTime) : remainingDebounceTime;\n    };\n    const handleTimeout = () => {\n        const currentTime = Date.now();\n        if (checkCanInvoke(currentTime)) {\n            return handleTrailing(currentTime);\n        }\n        timeoutId = setTimeout(handleTimeout, calculateRemainingWait(currentTime));\n    };\n    const debouncedFunction = function (...args) {\n        const currentTime = Date.now();\n        const canInvoke = checkCanInvoke(currentTime);\n        pendingArgs = args;\n        pendingThis = this;\n        lastCallTime = currentTime;\n        if (canInvoke) {\n            if (timeoutId === null) {\n                return handleLeading(currentTime);\n            }\n            if (hasMaxWait) {\n                clearTimeout(timeoutId);\n                timeoutId = setTimeout(handleTimeout, wait);\n                return invoke(currentTime);\n            }\n        }\n        if (timeoutId === null) {\n            timeoutId = setTimeout(handleTimeout, wait);\n        }\n        return lastResult;\n    };\n    debouncedFunction.cancel = () => {\n        if (timeoutId !== null) {\n            clearTimeout(timeoutId);\n        }\n        debounceStartTime = 0;\n        lastCallTime = pendingArgs = pendingThis = timeoutId = null;\n    };\n    debouncedFunction.flush = () => {\n        return timeoutId === null ? lastResult : handleTrailing(Date.now());\n    };\n    return debouncedFunction;\n}\n\nexports.debounce = debounce;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC5C,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC7BA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,YAAY,GAAG,IAAI;EACvB,IAAIC,iBAAiB,GAAG,CAAC;EACzB,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,UAAU;EACd,MAAM;IAAEC,OAAO,GAAG,KAAK;IAAEC,QAAQ,GAAG,IAAI;IAAEC;EAAQ,CAAC,GAAGT,OAAO;EAC7D,MAAMU,UAAU,GAAG,SAAS,IAAIV,OAAO;EACvC,MAAMW,SAAS,GAAGD,UAAU,GAAGE,IAAI,CAACC,GAAG,CAACC,MAAM,CAACL,OAAO,CAAC,IAAI,CAAC,EAAEV,IAAI,CAAC,GAAG,CAAC;EACvE,MAAMgB,MAAM,GAAIC,IAAI,IAAK;IACrB,IAAIf,WAAW,KAAK,IAAI,EAAE;MACtBK,UAAU,GAAGR,IAAI,CAACmB,KAAK,CAACf,WAAW,EAAED,WAAW,CAAC;IACrD;IACAA,WAAW,GAAGC,WAAW,GAAG,IAAI;IAChCE,iBAAiB,GAAGY,IAAI;IACxB,OAAOV,UAAU;EACrB,CAAC;EACD,MAAMY,aAAa,GAAIF,IAAI,IAAK;IAC5BZ,iBAAiB,GAAGY,IAAI;IACxBX,SAAS,GAAGc,UAAU,CAACC,aAAa,EAAErB,IAAI,CAAC;IAC3C,IAAIQ,OAAO,IAAIN,WAAW,KAAK,IAAI,EAAE;MACjC,OAAOc,MAAM,CAACC,IAAI,CAAC;IACvB;IACA,OAAOV,UAAU;EACrB,CAAC;EACD,MAAMe,cAAc,GAAIL,IAAI,IAAK;IAC7BX,SAAS,GAAG,IAAI;IAChB,IAAIG,QAAQ,IAAIP,WAAW,KAAK,IAAI,EAAE;MAClC,OAAOc,MAAM,CAACC,IAAI,CAAC;IACvB;IACA,OAAOV,UAAU;EACrB,CAAC;EACD,MAAMgB,cAAc,GAAIN,IAAI,IAAK;IAC7B,IAAIb,YAAY,KAAK,IAAI,EAAE;MACvB,OAAO,IAAI;IACf;IACA,MAAMoB,iBAAiB,GAAGP,IAAI,GAAGb,YAAY;IAC7C,MAAMqB,sBAAsB,GAAGD,iBAAiB,IAAIxB,IAAI,IAAIwB,iBAAiB,GAAG,CAAC;IACjF,MAAME,gBAAgB,GAAGf,UAAU,IAAIM,IAAI,GAAGZ,iBAAiB,IAAIO,SAAS;IAC5E,OAAOa,sBAAsB,IAAIC,gBAAgB;EACrD,CAAC;EACD,MAAMC,sBAAsB,GAAIV,IAAI,IAAK;IACrC,MAAMO,iBAAiB,GAAGpB,YAAY,KAAK,IAAI,GAAG,CAAC,GAAGa,IAAI,GAAGb,YAAY;IACzE,MAAMwB,qBAAqB,GAAG5B,IAAI,GAAGwB,iBAAiB;IACtD,MAAMK,oBAAoB,GAAGjB,SAAS,IAAIK,IAAI,GAAGZ,iBAAiB,CAAC;IACnE,OAAOM,UAAU,GAAGE,IAAI,CAACiB,GAAG,CAACF,qBAAqB,EAAEC,oBAAoB,CAAC,GAAGD,qBAAqB;EACrG,CAAC;EACD,MAAMP,aAAa,GAAGA,CAAA,KAAM;IACxB,MAAMU,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,IAAIV,cAAc,CAACQ,WAAW,CAAC,EAAE;MAC7B,OAAOT,cAAc,CAACS,WAAW,CAAC;IACtC;IACAzB,SAAS,GAAGc,UAAU,CAACC,aAAa,EAAEM,sBAAsB,CAACI,WAAW,CAAC,CAAC;EAC9E,CAAC;EACD,MAAMG,iBAAiB,GAAG,SAAAA,CAAU,GAAGC,IAAI,EAAE;IACzC,MAAMJ,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,MAAMG,SAAS,GAAGb,cAAc,CAACQ,WAAW,CAAC;IAC7C7B,WAAW,GAAGiC,IAAI;IAClBhC,WAAW,GAAG,IAAI;IAClBC,YAAY,GAAG2B,WAAW;IAC1B,IAAIK,SAAS,EAAE;MACX,IAAI9B,SAAS,KAAK,IAAI,EAAE;QACpB,OAAOa,aAAa,CAACY,WAAW,CAAC;MACrC;MACA,IAAIpB,UAAU,EAAE;QACZ0B,YAAY,CAAC/B,SAAS,CAAC;QACvBA,SAAS,GAAGc,UAAU,CAACC,aAAa,EAAErB,IAAI,CAAC;QAC3C,OAAOgB,MAAM,CAACe,WAAW,CAAC;MAC9B;IACJ;IACA,IAAIzB,SAAS,KAAK,IAAI,EAAE;MACpBA,SAAS,GAAGc,UAAU,CAACC,aAAa,EAAErB,IAAI,CAAC;IAC/C;IACA,OAAOO,UAAU;EACrB,CAAC;EACD2B,iBAAiB,CAACI,MAAM,GAAG,MAAM;IAC7B,IAAIhC,SAAS,KAAK,IAAI,EAAE;MACpB+B,YAAY,CAAC/B,SAAS,CAAC;IAC3B;IACAD,iBAAiB,GAAG,CAAC;IACrBD,YAAY,GAAGF,WAAW,GAAGC,WAAW,GAAGG,SAAS,GAAG,IAAI;EAC/D,CAAC;EACD4B,iBAAiB,CAACK,KAAK,GAAG,MAAM;IAC5B,OAAOjC,SAAS,KAAK,IAAI,GAAGC,UAAU,GAAGe,cAAc,CAACU,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EACvE,CAAC;EACD,OAAOC,iBAAiB;AAC5B;AAEAxC,OAAO,CAACI,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}