{"ast": null, "code": "const t0 = new Date(),\n  t1 = new Date();\nexport function timeInterval(floori, offseti, count, field) {\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date() : new Date(+date)), date;\n  }\n  interval.floor = date => {\n    return floori(date = new Date(+date)), date;\n  };\n  interval.ceil = date => {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n  interval.round = date => {\n    const d0 = interval(date),\n      d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n  interval.offset = (date, step) => {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n  interval.range = (start, stop, step) => {\n    const range = [];\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n    let previous;\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start); while (previous < start && start < stop);\n    return range;\n  };\n  interval.filter = test => {\n    return timeInterval(date => {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, (date, step) => {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n  if (count) {\n    interval.count = (start, end) => {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n    interval.every = step => {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? d => field(d) % step === 0 : d => interval.count(0, d) % step === 0);\n    };\n  }\n  return interval;\n}", "map": {"version": 3, "names": ["t0", "Date", "t1", "timeInterval", "floori", "offseti", "count", "field", "interval", "date", "arguments", "length", "floor", "ceil", "round", "d0", "d1", "offset", "step", "Math", "range", "start", "stop", "previous", "push", "filter", "test", "setTime", "end", "every", "isFinite", "d"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-time/src/interval.js"], "sourcesContent": ["const t0 = new Date, t1 = new Date;\n\nexport function timeInterval(floori, offseti, count, field) {\n\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n  }\n\n  interval.floor = (date) => {\n    return floori(date = new Date(+date)), date;\n  };\n\n  interval.ceil = (date) => {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n\n  interval.round = (date) => {\n    const d0 = interval(date), d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n\n  interval.offset = (date, step) => {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n\n  interval.range = (start, stop, step) => {\n    const range = [];\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n    let previous;\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n    while (previous < start && start < stop);\n    return range;\n  };\n\n  interval.filter = (test) => {\n    return timeInterval((date) => {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, (date, step) => {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n\n  if (count) {\n    interval.count = (start, end) => {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n\n    interval.every = (step) => {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null\n          : !(step > 1) ? interval\n          : interval.filter(field\n              ? (d) => field(d) % step === 0\n              : (d) => interval.count(0, d) % step === 0);\n    };\n  }\n\n  return interval;\n}\n"], "mappings": "AAAA,MAAMA,EAAE,GAAG,IAAIC,IAAI,CAAD,CAAC;EAAEC,EAAE,GAAG,IAAID,IAAI,CAAD,CAAC;AAElC,OAAO,SAASE,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAE1D,SAASC,QAAQA,CAACC,IAAI,EAAE;IACtB,OAAOL,MAAM,CAACK,IAAI,GAAGC,SAAS,CAACC,MAAM,KAAK,CAAC,GAAG,IAAIV,IAAI,CAAD,CAAC,GAAG,IAAIA,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC,EAAEA,IAAI;EACjF;EAEAD,QAAQ,CAACI,KAAK,GAAIH,IAAI,IAAK;IACzB,OAAOL,MAAM,CAACK,IAAI,GAAG,IAAIR,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC,EAAEA,IAAI;EAC7C,CAAC;EAEDD,QAAQ,CAACK,IAAI,GAAIJ,IAAI,IAAK;IACxB,OAAOL,MAAM,CAACK,IAAI,GAAG,IAAIR,IAAI,CAACQ,IAAI,GAAG,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAACI,IAAI,EAAE,CAAC,CAAC,EAAEL,MAAM,CAACK,IAAI,CAAC,EAAEA,IAAI;EAChF,CAAC;EAEDD,QAAQ,CAACM,KAAK,GAAIL,IAAI,IAAK;IACzB,MAAMM,EAAE,GAAGP,QAAQ,CAACC,IAAI,CAAC;MAAEO,EAAE,GAAGR,QAAQ,CAACK,IAAI,CAACJ,IAAI,CAAC;IACnD,OAAOA,IAAI,GAAGM,EAAE,GAAGC,EAAE,GAAGP,IAAI,GAAGM,EAAE,GAAGC,EAAE;EACxC,CAAC;EAEDR,QAAQ,CAACS,MAAM,GAAG,CAACR,IAAI,EAAES,IAAI,KAAK;IAChC,OAAOb,OAAO,CAACI,IAAI,GAAG,IAAIR,IAAI,CAAC,CAACQ,IAAI,CAAC,EAAES,IAAI,IAAI,IAAI,GAAG,CAAC,GAAGC,IAAI,CAACP,KAAK,CAACM,IAAI,CAAC,CAAC,EAAET,IAAI;EACnF,CAAC;EAEDD,QAAQ,CAACY,KAAK,GAAG,CAACC,KAAK,EAAEC,IAAI,EAAEJ,IAAI,KAAK;IACtC,MAAME,KAAK,GAAG,EAAE;IAChBC,KAAK,GAAGb,QAAQ,CAACK,IAAI,CAACQ,KAAK,CAAC;IAC5BH,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,CAAC,GAAGC,IAAI,CAACP,KAAK,CAACM,IAAI,CAAC;IAC1C,IAAI,EAAEG,KAAK,GAAGC,IAAI,CAAC,IAAI,EAAEJ,IAAI,GAAG,CAAC,CAAC,EAAE,OAAOE,KAAK,CAAC,CAAC;IAClD,IAAIG,QAAQ;IACZ,GAAGH,KAAK,CAACI,IAAI,CAACD,QAAQ,GAAG,IAAItB,IAAI,CAAC,CAACoB,KAAK,CAAC,CAAC,EAAEhB,OAAO,CAACgB,KAAK,EAAEH,IAAI,CAAC,EAAEd,MAAM,CAACiB,KAAK,CAAC,CAAC,QACzEE,QAAQ,GAAGF,KAAK,IAAIA,KAAK,GAAGC,IAAI;IACvC,OAAOF,KAAK;EACd,CAAC;EAEDZ,QAAQ,CAACiB,MAAM,GAAIC,IAAI,IAAK;IAC1B,OAAOvB,YAAY,CAAEM,IAAI,IAAK;MAC5B,IAAIA,IAAI,IAAIA,IAAI,EAAE,OAAOL,MAAM,CAACK,IAAI,CAAC,EAAE,CAACiB,IAAI,CAACjB,IAAI,CAAC,EAAEA,IAAI,CAACkB,OAAO,CAAClB,IAAI,GAAG,CAAC,CAAC;IAC5E,CAAC,EAAE,CAACA,IAAI,EAAES,IAAI,KAAK;MACjB,IAAIT,IAAI,IAAIA,IAAI,EAAE;QAChB,IAAIS,IAAI,GAAG,CAAC,EAAE,OAAO,EAAEA,IAAI,IAAI,CAAC,EAAE;UAChC,OAAOb,OAAO,CAACI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAACiB,IAAI,CAACjB,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,CAAC,MAAM,OAAO,EAAES,IAAI,IAAI,CAAC,EAAE;UACzB,OAAOb,OAAO,CAACI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAACiB,IAAI,CAACjB,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIH,KAAK,EAAE;IACTE,QAAQ,CAACF,KAAK,GAAG,CAACe,KAAK,EAAEO,GAAG,KAAK;MAC/B5B,EAAE,CAAC2B,OAAO,CAAC,CAACN,KAAK,CAAC,EAAEnB,EAAE,CAACyB,OAAO,CAAC,CAACC,GAAG,CAAC;MACpCxB,MAAM,CAACJ,EAAE,CAAC,EAAEI,MAAM,CAACF,EAAE,CAAC;MACtB,OAAOiB,IAAI,CAACP,KAAK,CAACN,KAAK,CAACN,EAAE,EAAEE,EAAE,CAAC,CAAC;IAClC,CAAC;IAEDM,QAAQ,CAACqB,KAAK,GAAIX,IAAI,IAAK;MACzBA,IAAI,GAAGC,IAAI,CAACP,KAAK,CAACM,IAAI,CAAC;MACvB,OAAO,CAACY,QAAQ,CAACZ,IAAI,CAAC,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,GACtC,EAAEA,IAAI,GAAG,CAAC,CAAC,GAAGV,QAAQ,GACtBA,QAAQ,CAACiB,MAAM,CAAClB,KAAK,GAChBwB,CAAC,IAAKxB,KAAK,CAACwB,CAAC,CAAC,GAAGb,IAAI,KAAK,CAAC,GAC3Ba,CAAC,IAAKvB,QAAQ,CAACF,KAAK,CAAC,CAAC,EAAEyB,CAAC,CAAC,GAAGb,IAAI,KAAK,CAAC,CAAC;IACrD,CAAC;EACH;EAEA,OAAOV,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}