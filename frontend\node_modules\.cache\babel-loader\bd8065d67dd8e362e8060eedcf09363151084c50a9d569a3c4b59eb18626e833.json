{"ast": null, "code": "export { default } from \"./merge.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/system/esm/merge/index.js"], "sourcesContent": ["export { default } from \"./merge.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}