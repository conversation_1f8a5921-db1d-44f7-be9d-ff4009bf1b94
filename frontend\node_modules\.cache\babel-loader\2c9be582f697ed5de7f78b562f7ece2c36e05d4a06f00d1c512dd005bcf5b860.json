{"ast": null, "code": "import { internal_serializeStyles } from '@mui/styled-engine';\nexport default function preprocessStyles(input) {\n  const {\n    variants,\n    ...style\n  } = input;\n  const result = {\n    variants,\n    style: internal_serializeStyles(style),\n    isProcessed: true\n  };\n\n  // Not supported on styled-components\n  if (result.style === style) {\n    return result;\n  }\n  if (variants) {\n    variants.forEach(variant => {\n      if (typeof variant.style !== 'function') {\n        variant.style = internal_serializeStyles(variant.style);\n      }\n    });\n  }\n  return result;\n}", "map": {"version": 3, "names": ["internal_serializeStyles", "preprocessStyles", "input", "variants", "style", "result", "isProcessed", "for<PERSON>ach", "variant"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/system/esm/preprocessStyles.js"], "sourcesContent": ["import { internal_serializeStyles } from '@mui/styled-engine';\nexport default function preprocessStyles(input) {\n  const {\n    variants,\n    ...style\n  } = input;\n  const result = {\n    variants,\n    style: internal_serializeStyles(style),\n    isProcessed: true\n  };\n\n  // Not supported on styled-components\n  if (result.style === style) {\n    return result;\n  }\n  if (variants) {\n    variants.forEach(variant => {\n      if (typeof variant.style !== 'function') {\n        variant.style = internal_serializeStyles(variant.style);\n      }\n    });\n  }\n  return result;\n}"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,oBAAoB;AAC7D,eAAe,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC9C,MAAM;IACJC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGF,KAAK;EACT,MAAMG,MAAM,GAAG;IACbF,QAAQ;IACRC,KAAK,EAAEJ,wBAAwB,CAACI,KAAK,CAAC;IACtCE,WAAW,EAAE;EACf,CAAC;;EAED;EACA,IAAID,MAAM,CAACD,KAAK,KAAKA,KAAK,EAAE;IAC1B,OAAOC,MAAM;EACf;EACA,IAAIF,QAAQ,EAAE;IACZA,QAAQ,CAACI,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,OAAOA,OAAO,CAACJ,KAAK,KAAK,UAAU,EAAE;QACvCI,OAAO,CAACJ,KAAK,GAAGJ,wBAAwB,CAACQ,OAAO,CAACJ,KAAK,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;EACA,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}