{"ast": null, "code": "export { default } from \"./setRef.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/utils/esm/setRef/index.js"], "sourcesContent": ["export { default } from \"./setRef.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}