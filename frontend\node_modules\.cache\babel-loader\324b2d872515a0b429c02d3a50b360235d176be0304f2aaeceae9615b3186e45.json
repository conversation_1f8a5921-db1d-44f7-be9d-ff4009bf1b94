{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function difference(values, ...others) {\n  values = new InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}", "map": {"version": 3, "names": ["InternSet", "difference", "values", "others", "other", "value", "delete"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-array/src/difference.js"], "sourcesContent": ["import {InternSet} from \"internmap\";\n\nexport default function difference(values, ...others) {\n  values = new InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAE,GAAGC,MAAM,EAAE;EACpDD,MAAM,GAAG,IAAIF,SAAS,CAACE,MAAM,CAAC;EAC9B,KAAK,MAAME,KAAK,IAAID,MAAM,EAAE;IAC1B,KAAK,MAAME,KAAK,IAAID,KAAK,EAAE;MACzBF,MAAM,CAACI,MAAM,CAACD,KAAK,CAAC;IACtB;EACF;EACA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}