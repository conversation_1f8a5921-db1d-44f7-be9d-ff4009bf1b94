{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { useEffect, useRef } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addCartesianGraphicalItem, addPolarGraphicalItem, removeCartesianGraphicalItem, removePolarGraphicalItem, replaceCartesianGraphicalItem } from './graphicalItemsSlice';\nimport { getNormalizedStackId } from '../util/ChartUtils';\nexport function SetCartesianGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  var prevPropsRef = useRef(null);\n  useEffect(() => {\n    var settings = _objectSpread(_objectSpread({}, props), {}, {\n      stackId: getNormalizedStackId(props.stackId)\n    });\n    if (prevPropsRef.current === null) {\n      dispatch(addCartesianGraphicalItem(settings));\n    } else if (prevPropsRef.current !== settings) {\n      dispatch(replaceCartesianGraphicalItem({\n        prev: prevPropsRef.current,\n        next: settings\n      }));\n    }\n    prevPropsRef.current = settings;\n  }, [dispatch, props]);\n  useEffect(() => {\n    return () => {\n      if (prevPropsRef.current) {\n        dispatch(removeCartesianGraphicalItem(prevPropsRef.current));\n        /*\n         * Here we have to reset the ref to null because in StrictMode, the effect will run twice,\n         * but it will keep the same ref value from the first render.\n         *\n         * In browser, React will clear the ref after the first effect cleanup,\n         * so that wouldn't be an issue.\n         *\n         * In StrictMode, however, the ref is kept,\n         * and in the hook above the code checks for `prevPropsRef.current === null`\n         * which would be false so it would not dispatch the `addCartesianGraphicalItem` action again.\n         *\n         * https://github.com/recharts/recharts/issues/6022\n         */\n        prevPropsRef.current = null;\n      }\n    };\n  }, [dispatch]);\n  return null;\n}\nexport function SetPolarGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addPolarGraphicalItem(props));\n    return () => {\n      dispatch(removePolarGraphicalItem(props));\n    };\n  }, [dispatch, props]);\n  return null;\n}", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "useEffect", "useRef", "useAppDispatch", "addCartesianGraphicalItem", "addPolarGraphicalItem", "removeCartesianGraphicalItem", "removePolarGraphicalItem", "replaceCartesianGraphicalItem", "getNormalizedStackId", "SetCartesianGraphicalItem", "props", "dispatch", "prevPropsRef", "settings", "stackId", "current", "prev", "next", "SetPolarGraphicalItem"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/recharts/es6/state/SetGraphicalItem.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { useEffect, useRef } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addCartesianGraphicalItem, addPolarGraphicalItem, removeCartesianGraphicalItem, removePolarGraphicalItem, replaceCartesianGraphicalItem } from './graphicalItemsSlice';\nimport { getNormalizedStackId } from '../util/ChartUtils';\nexport function SetCartesianGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  var prevPropsRef = useRef(null);\n  useEffect(() => {\n    var settings = _objectSpread(_objectSpread({}, props), {}, {\n      stackId: getNormalizedStackId(props.stackId)\n    });\n    if (prevPropsRef.current === null) {\n      dispatch(addCartesianGraphicalItem(settings));\n    } else if (prevPropsRef.current !== settings) {\n      dispatch(replaceCartesianGraphicalItem({\n        prev: prevPropsRef.current,\n        next: settings\n      }));\n    }\n    prevPropsRef.current = settings;\n  }, [dispatch, props]);\n  useEffect(() => {\n    return () => {\n      if (prevPropsRef.current) {\n        dispatch(removeCartesianGraphicalItem(prevPropsRef.current));\n        /*\n         * Here we have to reset the ref to null because in StrictMode, the effect will run twice,\n         * but it will keep the same ref value from the first render.\n         *\n         * In browser, React will clear the ref after the first effect cleanup,\n         * so that wouldn't be an issue.\n         *\n         * In StrictMode, however, the ref is kept,\n         * and in the hook above the code checks for `prevPropsRef.current === null`\n         * which would be false so it would not dispatch the `addCartesianGraphicalItem` action again.\n         *\n         * https://github.com/recharts/recharts/issues/6022\n         */\n        prevPropsRef.current = null;\n      }\n    };\n  }, [dispatch]);\n  return null;\n}\nexport function SetPolarGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addPolarGraphicalItem(props));\n    return () => {\n      dispatch(removePolarGraphicalItem(props));\n    };\n  }, [dispatch, props]);\n  return null;\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,yBAAyB,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,wBAAwB,EAAEC,6BAA6B,QAAQ,uBAAuB;AAC/K,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,OAAO,SAASC,yBAAyBA,CAACC,KAAK,EAAE;EAC/C,IAAIC,QAAQ,GAAGT,cAAc,CAAC,CAAC;EAC/B,IAAIU,YAAY,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,GAAGjC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8B,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACzDI,OAAO,EAAEN,oBAAoB,CAACE,KAAK,CAACI,OAAO;IAC7C,CAAC,CAAC;IACF,IAAIF,YAAY,CAACG,OAAO,KAAK,IAAI,EAAE;MACjCJ,QAAQ,CAACR,yBAAyB,CAACU,QAAQ,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAID,YAAY,CAACG,OAAO,KAAKF,QAAQ,EAAE;MAC5CF,QAAQ,CAACJ,6BAA6B,CAAC;QACrCS,IAAI,EAAEJ,YAAY,CAACG,OAAO;QAC1BE,IAAI,EAAEJ;MACR,CAAC,CAAC,CAAC;IACL;IACAD,YAAY,CAACG,OAAO,GAAGF,QAAQ;EACjC,CAAC,EAAE,CAACF,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrBV,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIY,YAAY,CAACG,OAAO,EAAE;QACxBJ,QAAQ,CAACN,4BAA4B,CAACO,YAAY,CAACG,OAAO,CAAC,CAAC;QAC5D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACQH,YAAY,CAACG,OAAO,GAAG,IAAI;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;EACd,OAAO,IAAI;AACb;AACA,OAAO,SAASO,qBAAqBA,CAACR,KAAK,EAAE;EAC3C,IAAIC,QAAQ,GAAGT,cAAc,CAAC,CAAC;EAC/BF,SAAS,CAAC,MAAM;IACdW,QAAQ,CAACP,qBAAqB,CAACM,KAAK,CAAC,CAAC;IACtC,OAAO,MAAM;MACXC,QAAQ,CAACL,wBAAwB,CAACI,KAAK,CAAC,CAAC;IAC3C,CAAC;EACH,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}