{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isArrayLike = require('./isArrayLike.js');\nconst isObjectLike = require('./isObjectLike.js');\nfunction isArrayLikeObject(value) {\n  return isObjectLike.isObjectLike(value) && isArrayLike.isArrayLike(value);\n}\nexports.isArrayLikeObject = isArrayLikeObject;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isArrayLike", "require", "isObjectLike", "isArrayLikeObject"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/es-toolkit/dist/compat/predicate/isArrayLikeObject.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isArrayLike = require('./isArrayLike.js');\nconst isObjectLike = require('./isObjectLike.js');\n\nfunction isArrayLikeObject(value) {\n    return isObjectLike.isObjectLike(value) && isArrayLike.isArrayLike(value);\n}\n\nexports.isArrayLikeObject = isArrayLikeObject;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,WAAW,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC/C,MAAMC,YAAY,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAEjD,SAASE,iBAAiBA,CAACJ,KAAK,EAAE;EAC9B,OAAOG,YAAY,CAACA,YAAY,CAACH,KAAK,CAAC,IAAIC,WAAW,CAACA,WAAW,CAACD,KAAK,CAAC;AAC7E;AAEAH,OAAO,CAACO,iBAAiB,GAAGA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}