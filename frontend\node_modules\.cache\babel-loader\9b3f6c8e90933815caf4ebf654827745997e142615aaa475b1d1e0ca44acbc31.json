{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nexport var selectChartOffset = createSelector([selectChartOffsetInternal], offsetInternal => {\n  if (!offsetInternal) {\n    return undefined;\n  }\n  return {\n    top: offsetInternal.top,\n    bottom: offsetInternal.bottom,\n    left: offsetInternal.left,\n    right: offsetInternal.right\n  };\n});", "map": {"version": 3, "names": ["createSelector", "selectChartOffsetInternal", "selectChartOffset", "offsetInternal", "undefined", "top", "bottom", "left", "right"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/recharts/es6/state/selectors/selectChartOffset.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nexport var selectChartOffset = createSelector([selectChartOffsetInternal], offsetInternal => {\n  if (!offsetInternal) {\n    return undefined;\n  }\n  return {\n    top: offsetInternal.top,\n    bottom: offsetInternal.bottom,\n    left: offsetInternal.left,\n    right: offsetInternal.right\n  };\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,OAAO,IAAIC,iBAAiB,GAAGF,cAAc,CAAC,CAACC,yBAAyB,CAAC,EAAEE,cAAc,IAAI;EAC3F,IAAI,CAACA,cAAc,EAAE;IACnB,OAAOC,SAAS;EAClB;EACA,OAAO;IACLC,GAAG,EAAEF,cAAc,CAACE,GAAG;IACvBC,MAAM,EAAEH,cAAc,CAACG,MAAM;IAC7BC,IAAI,EAAEJ,cAAc,CAACI,IAAI;IACzBC,KAAK,EAAEL,cAAc,CAACK;EACxB,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}