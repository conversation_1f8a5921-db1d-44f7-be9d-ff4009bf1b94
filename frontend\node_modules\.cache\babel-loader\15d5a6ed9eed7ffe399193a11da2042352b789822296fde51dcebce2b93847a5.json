{"ast": null, "code": "var _excluded = [\"option\", \"isActive\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { Symbols } from '../shape/Symbols';\nimport { Shape } from './ActiveShapeUtils';\nexport function ScatterSymbol(_ref) {\n  var {\n      option,\n      isActive\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  if (typeof option === 'string') {\n    return /*#__PURE__*/React.createElement(Shape, _extends({\n      option: /*#__PURE__*/React.createElement(Symbols, _extends({\n        type: option\n      }, props)),\n      isActive: isActive,\n      shapeType: \"symbols\"\n    }, props));\n  }\n  return /*#__PURE__*/React.createElement(Shape, _extends({\n    option: option,\n    isActive: isActive,\n    shapeType: \"symbols\"\n  }, props));\n}", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "React", "Symbols", "<PERSON><PERSON><PERSON>", "ScatterSymbol", "_ref", "option", "isActive", "props", "createElement", "type", "shapeType"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/recharts/es6/util/ScatterUtils.js"], "sourcesContent": ["var _excluded = [\"option\", \"isActive\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { Symbols } from '../shape/Symbols';\nimport { Shape } from './ActiveShapeUtils';\nexport function ScatterSymbol(_ref) {\n  var {\n      option,\n      isActive\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  if (typeof option === 'string') {\n    return /*#__PURE__*/React.createElement(Shape, _extends({\n      option: /*#__PURE__*/React.createElement(Symbols, _extends({\n        type: option\n      }, props)),\n      isActive: isActive,\n      shapeType: \"symbols\"\n    }, props));\n  }\n  return /*#__PURE__*/React.createElement(Shape, _extends({\n    option: option,\n    isActive: isActive,\n    shapeType: \"symbols\"\n  }, props));\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;AACtC,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,OAAO,KAAKY,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI;MACAC,MAAM;MACNC;IACF,CAAC,GAAGF,IAAI;IACRG,KAAK,GAAGd,wBAAwB,CAACW,IAAI,EAAEzB,SAAS,CAAC;EACnD,IAAI,OAAO0B,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO,aAAaL,KAAK,CAACQ,aAAa,CAACN,KAAK,EAAEtB,QAAQ,CAAC;MACtDyB,MAAM,EAAE,aAAaL,KAAK,CAACQ,aAAa,CAACP,OAAO,EAAErB,QAAQ,CAAC;QACzD6B,IAAI,EAAEJ;MACR,CAAC,EAAEE,KAAK,CAAC,CAAC;MACVD,QAAQ,EAAEA,QAAQ;MAClBI,SAAS,EAAE;IACb,CAAC,EAAEH,KAAK,CAAC,CAAC;EACZ;EACA,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAACN,KAAK,EAAEtB,QAAQ,CAAC;IACtDyB,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClBI,SAAS,EAAE;EACb,CAAC,EAAEH,KAAK,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}