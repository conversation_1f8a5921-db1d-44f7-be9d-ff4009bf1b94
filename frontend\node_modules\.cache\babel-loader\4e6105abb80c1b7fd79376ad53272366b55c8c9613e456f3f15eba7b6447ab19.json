{"ast": null, "code": "import { sqrt } from \"../math.js\";\nexport default {\n  draw(context, size) {\n    const r = sqrt(size) * 0.4431;\n    context.moveTo(r, r);\n    context.lineTo(r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, r);\n    context.closePath();\n  }\n};", "map": {"version": 3, "names": ["sqrt", "draw", "context", "size", "r", "moveTo", "lineTo", "closePath"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-shape/src/symbol/square2.js"], "sourcesContent": ["import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size) * 0.4431;\n    context.moveTo(r, r);\n    context.lineTo(r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, r);\n    context.closePath();\n  }\n};\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,YAAY;AAE/B,eAAe;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAGJ,IAAI,CAACG,IAAI,CAAC,GAAG,MAAM;IAC7BD,OAAO,CAACG,MAAM,CAACD,CAAC,EAAEA,CAAC,CAAC;IACpBF,OAAO,CAACI,MAAM,CAACF,CAAC,EAAE,CAACA,CAAC,CAAC;IACrBF,OAAO,CAACI,MAAM,CAAC,CAACF,CAAC,EAAE,CAACA,CAAC,CAAC;IACtBF,OAAO,CAACI,MAAM,CAAC,CAACF,CAAC,EAAEA,CAAC,CAAC;IACrBF,OAAO,CAACK,SAAS,CAAC,CAAC;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}