{"ast": null, "code": "module.exports = require('../dist/compat/array/last.js').last;", "map": {"version": 3, "names": ["module", "exports", "require", "last"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/es-toolkit/compat/last.js"], "sourcesContent": ["module.exports = require('../dist/compat/array/last.js').last;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,8BAA8B,CAAC,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}