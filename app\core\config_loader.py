"""
Configuration loader that combines YAML config with environment settings.
"""

import os
import yaml
from typing import Any, Dict, List
from pathlib import Path
from dataclasses import dataclass

@dataclass
class GeneralConfig:
    """General configuration settings."""
    env_path: str = '.env'
    output_dir: str = 'reports'
    fyers_api_url: List[str] = None
    
    def __post_init__(self):
        if self.fyers_api_url is None:
            self.fyers_api_url = [
                'https://public.fyers.in/sym_details/NSE_CM.csv',
                'https://public.fyers.in/sym_details/NSE_FO.csv'
            ]

@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    host: str = 'localhost'
    port: int = 5432
    name: str = 'nse_db'
    user: str = 'postgres'
    password: str = 'your_password'
    timescale_enabled: bool = True

@dataclass
class TimeframeConfig:
    """Timeframe configuration settings."""
    interval: int = 1
    days_to_fetch: int = 90

@dataclass
class RateLimitConfig:
    """Rate limiting configuration settings."""
    min_delay_seconds: float = 0.1
    max_retries: int = 5
    retry_backoff: float = 3.0

@dataclass
class MarketFiltersConfig:
    """Market filter configuration settings."""
    min_volume: int = 11
    max_volume: int = ***********
    min_ltp_price: float = 1.0
    max_ltp_price: float = 100000.0

@dataclass
class Config:
    """Main configuration class."""
    general: GeneralConfig
    database: DatabaseConfig
    timeframe: TimeframeConfig
    rate_limit: RateLimitConfig
    market_filters: MarketFiltersConfig
    symbols: List[str]
    market_types: List[str]

class ConfigLoader:
    """Configuration loader that combines YAML and environment variables."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the config loader."""
        self.config_path = config_path
        self._config = None
    
    def load_config(self) -> Config:
        """Load configuration from YAML file and environment variables."""
        if self._config is not None:
            return self._config
        
        # Load YAML config
        yaml_config = self._load_yaml_config()
        
        # Create configuration objects
        general = GeneralConfig(
            env_path=yaml_config.get('general', {}).get('env_path', '.env'),
            output_dir=yaml_config.get('general', {}).get('output_dir', 'reports'),
            fyers_api_url=yaml_config.get('general', {}).get('fyers_api_url', [
                'https://public.fyers.in/sym_details/NSE_CM.csv',
                'https://public.fyers.in/sym_details/NSE_FO.csv'
            ])
        )
        
        database = DatabaseConfig(
            host=yaml_config.get('database', {}).get('host', 'localhost'),
            port=yaml_config.get('database', {}).get('port', 5432),
            name=yaml_config.get('database', {}).get('name', 'nse_db'),
            user=yaml_config.get('database', {}).get('user', 'postgres'),
            password=yaml_config.get('database', {}).get('password', 'your_password'),
            timescale_enabled=yaml_config.get('database', {}).get('timescale_enabled', True)
        )
        
        timeframe = TimeframeConfig(
            interval=yaml_config.get('timeframe', {}).get('interval', 1),
            days_to_fetch=yaml_config.get('timeframe', {}).get('days_to_fetch', 90)
        )
        
        rate_limit = RateLimitConfig(
            min_delay_seconds=yaml_config.get('rate_limit', {}).get('min_delay_seconds', 0.1),
            max_retries=yaml_config.get('rate_limit', {}).get('max_retries', 5),
            retry_backoff=yaml_config.get('rate_limit', {}).get('retry_backoff', 3.0)
        )
        
        market_filters = MarketFiltersConfig(
            min_volume=yaml_config.get('market_filters', {}).get('min_volume', 11),
            max_volume=yaml_config.get('market_filters', {}).get('max_volume', ***********),
            min_ltp_price=yaml_config.get('market_filters', {}).get('min_ltp_price', 1.0),
            max_ltp_price=yaml_config.get('market_filters', {}).get('max_ltp_price', 100000.0)
        )
        
        symbols = yaml_config.get('symbols', ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'RELIANCE'])
        market_types = yaml_config.get('market_types', ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'])
        
        self._config = Config(
            general=general,
            database=database,
            timeframe=timeframe,
            rate_limit=rate_limit,
            market_filters=market_filters,
            symbols=symbols,
            market_types=market_types
        )
        
        return self._config
    
    def _load_yaml_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as file:
                    return yaml.safe_load(file) or {}
            except Exception as e:
                print(f"Warning: Failed to load YAML config: {e}")
                return {}
        else:
            print(f"Warning: Config file {self.config_path} not found, using defaults")
            return {}

# Global config loader instance
_config_loader = ConfigLoader()

def get_config() -> Config:
    """Get the global configuration."""
    return _config_loader.load_config()

# For backward compatibility
def get_yaml_config() -> Dict[str, Any]:
    """Get raw YAML configuration."""
    return _config_loader._load_yaml_config()
