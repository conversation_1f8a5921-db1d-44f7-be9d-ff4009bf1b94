{"ast": null, "code": "export default function (grouping, thousands) {\n  return function (value, width) {\n    var i = value.length,\n      t = [],\n      j = 0,\n      g = grouping[0],\n      length = 0;\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n    return t.reverse().join(thousands);\n  };\n}", "map": {"version": 3, "names": ["grouping", "thousands", "value", "width", "i", "length", "t", "j", "g", "Math", "max", "push", "substring", "reverse", "join"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-format/src/formatGroup.js"], "sourcesContent": ["export default function(grouping, thousands) {\n  return function(value, width) {\n    var i = value.length,\n        t = [],\n        j = 0,\n        g = grouping[0],\n        length = 0;\n\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n\n    return t.reverse().join(thousands);\n  };\n}\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAEC,SAAS,EAAE;EAC3C,OAAO,UAASC,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAIC,CAAC,GAAGF,KAAK,CAACG,MAAM;MAChBC,CAAC,GAAG,EAAE;MACNC,CAAC,GAAG,CAAC;MACLC,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC;MACfK,MAAM,GAAG,CAAC;IAEd,OAAOD,CAAC,GAAG,CAAC,IAAII,CAAC,GAAG,CAAC,EAAE;MACrB,IAAIH,MAAM,GAAGG,CAAC,GAAG,CAAC,GAAGL,KAAK,EAAEK,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,KAAK,GAAGE,MAAM,CAAC;MAC3DC,CAAC,CAACK,IAAI,CAACT,KAAK,CAACU,SAAS,CAACR,CAAC,IAAII,CAAC,EAAEJ,CAAC,GAAGI,CAAC,CAAC,CAAC;MACtC,IAAI,CAACH,MAAM,IAAIG,CAAC,GAAG,CAAC,IAAIL,KAAK,EAAE;MAC/BK,CAAC,GAAGR,QAAQ,CAACO,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,IAAIP,QAAQ,CAACK,MAAM,CAAC;IAC7C;IAEA,OAAOC,CAAC,CAACO,OAAO,CAAC,CAAC,CAACC,IAAI,CAACb,SAAS,CAAC;EACpC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}