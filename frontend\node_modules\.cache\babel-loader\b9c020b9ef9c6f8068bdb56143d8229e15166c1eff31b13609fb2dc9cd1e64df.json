{"ast": null, "code": "module.exports = require('../dist/compat/function/throttle.js').throttle;", "map": {"version": 3, "names": ["module", "exports", "require", "throttle"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/es-toolkit/compat/throttle.js"], "sourcesContent": ["module.exports = require('../dist/compat/function/throttle.js').throttle;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,qCAAqC,CAAC,CAACC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}