{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst TableContext = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  TableContext.displayName = 'TableContext';\n}\nexport default TableContext;", "map": {"version": 3, "names": ["React", "TableContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/material/esm/Table/TableContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst TableContext = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  TableContext.displayName = 'TableContext';\n}\nexport default TableContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC;AACvD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,YAAY,CAACK,WAAW,GAAG,cAAc;AAC3C;AACA,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}