{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 04:57:37,048+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 04:57:37,052+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 651, in __auth_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 04:57:37,421+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 04:57:37,422+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 813, in __lite_full_mode_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-13 04:57:37,792+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 04:57:37,792+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 21:38:20,532+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 21:38:20,536+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 651, in __auth_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 21:38:20,767+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 21:38:20,768+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 813, in __lite_full_mode_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-13 21:38:21,794+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 21:38:21,795+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
