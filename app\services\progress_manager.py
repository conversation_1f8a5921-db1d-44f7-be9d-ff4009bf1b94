"""
Progress Management System for Long-Running Data Operations.
Provides persistence, recovery, and monitoring capabilities.
"""

import logging
import json
import sqlite3
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import threading
import time

logger = logging.getLogger(__name__)

@dataclass
class ProgressSnapshot:
    """Snapshot of operation progress."""
    operation_id: str
    operation_type: str
    status: str  # 'running', 'paused', 'completed', 'failed'
    progress_percent: float
    current_step: str
    total_steps: int
    completed_steps: int
    start_time: datetime
    last_update: datetime
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class OperationMetrics:
    """Metrics for operation performance."""
    operation_id: str
    records_processed: int = 0
    records_per_second: float = 0.0
    bytes_processed: int = 0
    api_calls_made: int = 0
    errors_encountered: int = 0
    retries_attempted: int = 0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0

class ProgressManager:
    """Manager for operation progress persistence and monitoring."""
    
    def __init__(self, db_path: str = "progress/progress.db"):
        """
        Initialize progress manager.
        
        Args:
            db_path: Path to SQLite database for progress storage
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Active operations
        self._active_operations: Dict[str, ProgressSnapshot] = {}
        self._operation_callbacks: Dict[str, List[Callable]] = {}
        
        # Initialize database
        self._init_database()
        
        # Load active operations
        self._load_active_operations()
        
        logger.info(f"Progress manager initialized with database: {self.db_path}")
    
    def _init_database(self):
        """Initialize SQLite database for progress storage."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS progress_snapshots (
                    operation_id TEXT PRIMARY KEY,
                    operation_type TEXT NOT NULL,
                    status TEXT NOT NULL,
                    progress_percent REAL NOT NULL,
                    current_step TEXT NOT NULL,
                    total_steps INTEGER NOT NULL,
                    completed_steps INTEGER NOT NULL,
                    start_time TEXT NOT NULL,
                    last_update TEXT NOT NULL,
                    estimated_completion TEXT,
                    error_message TEXT,
                    metadata TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS operation_metrics (
                    operation_id TEXT PRIMARY KEY,
                    records_processed INTEGER DEFAULT 0,
                    records_per_second REAL DEFAULT 0.0,
                    bytes_processed INTEGER DEFAULT 0,
                    api_calls_made INTEGER DEFAULT 0,
                    errors_encountered INTEGER DEFAULT 0,
                    retries_attempted INTEGER DEFAULT 0,
                    memory_usage_mb REAL DEFAULT 0.0,
                    cpu_usage_percent REAL DEFAULT 0.0,
                    FOREIGN KEY (operation_id) REFERENCES progress_snapshots (operation_id)
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_status ON progress_snapshots (status)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_operation_type ON progress_snapshots (operation_type)
            """)
            
            conn.commit()
    
    def start_operation(
        self,
        operation_id: str,
        operation_type: str,
        total_steps: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ProgressSnapshot:
        """
        Start tracking a new operation.
        
        Args:
            operation_id: Unique operation identifier
            operation_type: Type of operation (e.g., 'historical_data_load')
            total_steps: Total number of steps in the operation
            metadata: Additional metadata
            
        Returns:
            ProgressSnapshot object
        """
        with self._lock:
            now = datetime.now()
            
            snapshot = ProgressSnapshot(
                operation_id=operation_id,
                operation_type=operation_type,
                status='running',
                progress_percent=0.0,
                current_step='Starting operation',
                total_steps=total_steps,
                completed_steps=0,
                start_time=now,
                last_update=now,
                metadata=metadata or {}
            )
            
            # Store in memory
            self._active_operations[operation_id] = snapshot
            self._operation_callbacks[operation_id] = []
            
            # Persist to database
            self._save_snapshot(snapshot)
            
            # Initialize metrics
            metrics = OperationMetrics(operation_id=operation_id)
            self._save_metrics(metrics)
            
            logger.info(f"Started tracking operation: {operation_id} ({operation_type})")
            return snapshot
    
    def update_progress(
        self,
        operation_id: str,
        completed_steps: Optional[int] = None,
        current_step: Optional[str] = None,
        metadata_update: Optional[Dict[str, Any]] = None
    ):
        """
        Update operation progress.
        
        Args:
            operation_id: Operation identifier
            completed_steps: Number of completed steps
            current_step: Description of current step
            metadata_update: Metadata updates
        """
        with self._lock:
            if operation_id not in self._active_operations:
                logger.warning(f"Operation {operation_id} not found for progress update")
                return
            
            snapshot = self._active_operations[operation_id]
            
            # Update fields
            if completed_steps is not None:
                snapshot.completed_steps = completed_steps
                snapshot.progress_percent = (completed_steps / snapshot.total_steps) * 100
                
                # Estimate completion time
                if completed_steps > 0:
                    elapsed = datetime.now() - snapshot.start_time
                    rate = completed_steps / elapsed.total_seconds()
                    remaining_steps = snapshot.total_steps - completed_steps
                    if rate > 0:
                        eta_seconds = remaining_steps / rate
                        snapshot.estimated_completion = datetime.now() + timedelta(seconds=eta_seconds)
            
            if current_step is not None:
                snapshot.current_step = current_step
            
            if metadata_update:
                snapshot.metadata.update(metadata_update)
            
            snapshot.last_update = datetime.now()
            
            # Persist to database
            self._save_snapshot(snapshot)
            
            # Trigger callbacks
            for callback in self._operation_callbacks.get(operation_id, []):
                try:
                    callback(snapshot)
                except Exception as e:
                    logger.error(f"Error in progress callback: {e}")
    
    def update_metrics(
        self,
        operation_id: str,
        records_processed: Optional[int] = None,
        bytes_processed: Optional[int] = None,
        api_calls_made: Optional[int] = None,
        errors_encountered: Optional[int] = None,
        retries_attempted: Optional[int] = None
    ):
        """
        Update operation metrics.
        
        Args:
            operation_id: Operation identifier
            records_processed: Number of records processed
            bytes_processed: Bytes processed
            api_calls_made: API calls made
            errors_encountered: Errors encountered
            retries_attempted: Retries attempted
        """
        with self._lock:
            metrics = self._load_metrics(operation_id)
            if not metrics:
                metrics = OperationMetrics(operation_id=operation_id)
            
            # Update metrics
            if records_processed is not None:
                metrics.records_processed = records_processed
                
                # Calculate rate
                if operation_id in self._active_operations:
                    snapshot = self._active_operations[operation_id]
                    elapsed = (datetime.now() - snapshot.start_time).total_seconds()
                    if elapsed > 0:
                        metrics.records_per_second = records_processed / elapsed
            
            if bytes_processed is not None:
                metrics.bytes_processed = bytes_processed
            
            if api_calls_made is not None:
                metrics.api_calls_made = api_calls_made
            
            if errors_encountered is not None:
                metrics.errors_encountered = errors_encountered
            
            if retries_attempted is not None:
                metrics.retries_attempted = retries_attempted
            
            # Save metrics
            self._save_metrics(metrics)
    
    def complete_operation(self, operation_id: str, success: bool = True, error_message: Optional[str] = None):
        """
        Mark operation as completed.
        
        Args:
            operation_id: Operation identifier
            success: Whether operation completed successfully
            error_message: Error message if failed
        """
        with self._lock:
            if operation_id not in self._active_operations:
                logger.warning(f"Operation {operation_id} not found for completion")
                return
            
            snapshot = self._active_operations[operation_id]
            snapshot.status = 'completed' if success else 'failed'
            snapshot.last_update = datetime.now()
            
            if not success and error_message:
                snapshot.error_message = error_message
            
            if success:
                snapshot.progress_percent = 100.0
                snapshot.completed_steps = snapshot.total_steps
                snapshot.current_step = 'Completed'
            
            # Persist to database
            self._save_snapshot(snapshot)
            
            # Remove from active operations
            del self._active_operations[operation_id]
            if operation_id in self._operation_callbacks:
                del self._operation_callbacks[operation_id]
            
            status_msg = "completed successfully" if success else f"failed: {error_message}"
            logger.info(f"Operation {operation_id} {status_msg}")
    
    def pause_operation(self, operation_id: str):
        """Pause an operation."""
        with self._lock:
            if operation_id in self._active_operations:
                snapshot = self._active_operations[operation_id]
                snapshot.status = 'paused'
                snapshot.last_update = datetime.now()
                self._save_snapshot(snapshot)
                logger.info(f"Operation {operation_id} paused")
    
    def resume_operation(self, operation_id: str):
        """Resume a paused operation."""
        with self._lock:
            if operation_id in self._active_operations:
                snapshot = self._active_operations[operation_id]
                snapshot.status = 'running'
                snapshot.last_update = datetime.now()
                self._save_snapshot(snapshot)
                logger.info(f"Operation {operation_id} resumed")
    
    def get_operation_status(self, operation_id: str) -> Optional[ProgressSnapshot]:
        """Get current status of an operation."""
        with self._lock:
            if operation_id in self._active_operations:
                return self._active_operations[operation_id]
            
            # Try loading from database
            return self._load_snapshot(operation_id)
    
    def get_operation_metrics(self, operation_id: str) -> Optional[OperationMetrics]:
        """Get metrics for an operation."""
        return self._load_metrics(operation_id)
    
    def list_operations(self, status_filter: Optional[str] = None) -> List[ProgressSnapshot]:
        """
        List operations.
        
        Args:
            status_filter: Filter by status ('running', 'completed', 'failed', 'paused')
            
        Returns:
            List of ProgressSnapshot objects
        """
        with sqlite3.connect(self.db_path) as conn:
            if status_filter:
                cursor = conn.execute(
                    "SELECT * FROM progress_snapshots WHERE status = ? ORDER BY last_update DESC",
                    (status_filter,)
                )
            else:
                cursor = conn.execute(
                    "SELECT * FROM progress_snapshots ORDER BY last_update DESC"
                )
            
            snapshots = []
            for row in cursor.fetchall():
                snapshot = self._row_to_snapshot(row)
                snapshots.append(snapshot)
            
            return snapshots
    
    def add_progress_callback(self, operation_id: str, callback: Callable[[ProgressSnapshot], None]):
        """Add a callback for progress updates."""
        with self._lock:
            if operation_id not in self._operation_callbacks:
                self._operation_callbacks[operation_id] = []
            self._operation_callbacks[operation_id].append(callback)
    
    def cleanup_old_operations(self, days_old: int = 30):
        """Clean up old completed/failed operations."""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "DELETE FROM operation_metrics WHERE operation_id IN "
                "(SELECT operation_id FROM progress_snapshots WHERE "
                "status IN ('completed', 'failed') AND last_update < ?)",
                (cutoff_date.isoformat(),)
            )
            
            cursor = conn.execute(
                "DELETE FROM progress_snapshots WHERE "
                "status IN ('completed', 'failed') AND last_update < ?",
                (cutoff_date.isoformat(),)
            )
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            logger.info(f"Cleaned up {deleted_count} old operations")
    
    def _save_snapshot(self, snapshot: ProgressSnapshot):
        """Save snapshot to database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO progress_snapshots VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                snapshot.operation_id,
                snapshot.operation_type,
                snapshot.status,
                snapshot.progress_percent,
                snapshot.current_step,
                snapshot.total_steps,
                snapshot.completed_steps,
                snapshot.start_time.isoformat(),
                snapshot.last_update.isoformat(),
                snapshot.estimated_completion.isoformat() if snapshot.estimated_completion else None,
                snapshot.error_message,
                json.dumps(snapshot.metadata) if snapshot.metadata else None
            ))
            conn.commit()
    
    def _load_snapshot(self, operation_id: str) -> Optional[ProgressSnapshot]:
        """Load snapshot from database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM progress_snapshots WHERE operation_id = ?",
                (operation_id,)
            )
            row = cursor.fetchone()
            
            if row:
                return self._row_to_snapshot(row)
            return None
    
    def _row_to_snapshot(self, row) -> ProgressSnapshot:
        """Convert database row to ProgressSnapshot."""
        return ProgressSnapshot(
            operation_id=row[0],
            operation_type=row[1],
            status=row[2],
            progress_percent=row[3],
            current_step=row[4],
            total_steps=row[5],
            completed_steps=row[6],
            start_time=datetime.fromisoformat(row[7]),
            last_update=datetime.fromisoformat(row[8]),
            estimated_completion=datetime.fromisoformat(row[9]) if row[9] else None,
            error_message=row[10],
            metadata=json.loads(row[11]) if row[11] else {}
        )
    
    def _save_metrics(self, metrics: OperationMetrics):
        """Save metrics to database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO operation_metrics VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metrics.operation_id,
                metrics.records_processed,
                metrics.records_per_second,
                metrics.bytes_processed,
                metrics.api_calls_made,
                metrics.errors_encountered,
                metrics.retries_attempted,
                metrics.memory_usage_mb,
                metrics.cpu_usage_percent
            ))
            conn.commit()
    
    def _load_metrics(self, operation_id: str) -> Optional[OperationMetrics]:
        """Load metrics from database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT * FROM operation_metrics WHERE operation_id = ?",
                (operation_id,)
            )
            row = cursor.fetchone()
            
            if row:
                return OperationMetrics(*row)
            return None
    
    def _load_active_operations(self):
        """Load active operations from database."""
        active_snapshots = self.list_operations(status_filter='running')
        active_snapshots.extend(self.list_operations(status_filter='paused'))
        
        for snapshot in active_snapshots:
            self._active_operations[snapshot.operation_id] = snapshot
            self._operation_callbacks[snapshot.operation_id] = []
        
        logger.info(f"Loaded {len(active_snapshots)} active operations from database")
