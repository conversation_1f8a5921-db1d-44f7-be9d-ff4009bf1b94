{"ast": null, "code": "import none from \"./none.js\";\nexport default function (series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n  none(series, order);\n}", "map": {"version": 3, "names": ["none", "series", "order", "n", "length", "j", "s0", "m", "i", "y"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-shape/src/offset/silhouette.js"], "sourcesContent": ["import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n  none(series, order);\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,MAAM,EAAEC,KAAK,EAAE;EACrC,IAAI,EAAE,CAACC,CAAC,GAAGF,MAAM,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGL,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEC,CAAC,EAAEI,CAAC,GAAGD,EAAE,CAACF,MAAM,EAAEC,CAAC,GAAGE,CAAC,EAAE,EAAEF,CAAC,EAAE;IACnE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAEC,CAAC,IAAIR,MAAM,CAACO,CAAC,CAAC,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5DC,EAAE,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIC,EAAE,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACI,CAAC,GAAG,CAAC;EAC/B;EACAT,IAAI,CAACC,MAAM,EAAEC,KAAK,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}