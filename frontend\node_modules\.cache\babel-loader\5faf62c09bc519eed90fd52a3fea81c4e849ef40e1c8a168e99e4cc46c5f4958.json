{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction flatten(arr, depth = 1) {\n  const result = [];\n  const flooredDepth = Math.floor(depth);\n  const recursive = (arr, currentDepth) => {\n    for (let i = 0; i < arr.length; i++) {\n      const item = arr[i];\n      if (Array.isArray(item) && currentDepth < flooredDepth) {\n        recursive(item, currentDepth + 1);\n      } else {\n        result.push(item);\n      }\n    }\n  };\n  recursive(arr, 0);\n  return result;\n}\nexports.flatten = flatten;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "flatten", "arr", "depth", "result", "flooredDepth", "Math", "floor", "recursive", "<PERSON><PERSON><PERSON><PERSON>", "i", "length", "item", "Array", "isArray", "push"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/es-toolkit/dist/array/flatten.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction flatten(arr, depth = 1) {\n    const result = [];\n    const flooredDepth = Math.floor(depth);\n    const recursive = (arr, currentDepth) => {\n        for (let i = 0; i < arr.length; i++) {\n            const item = arr[i];\n            if (Array.isArray(item) && currentDepth < flooredDepth) {\n                recursive(item, currentDepth + 1);\n            }\n            else {\n                result.push(item);\n            }\n        }\n    };\n    recursive(arr, 0);\n    return result;\n}\n\nexports.flatten = flatten;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,OAAOA,CAACC,GAAG,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC7B,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC;EACtC,MAAMK,SAAS,GAAGA,CAACN,GAAG,EAAEO,YAAY,KAAK;IACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MACjC,MAAME,IAAI,GAAGV,GAAG,CAACQ,CAAC,CAAC;MACnB,IAAIG,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAIH,YAAY,GAAGJ,YAAY,EAAE;QACpDG,SAAS,CAACI,IAAI,EAAEH,YAAY,GAAG,CAAC,CAAC;MACrC,CAAC,MACI;QACDL,MAAM,CAACW,IAAI,CAACH,IAAI,CAAC;MACrB;IACJ;EACJ,CAAC;EACDJ,SAAS,CAACN,GAAG,EAAE,CAAC,CAAC;EACjB,OAAOE,MAAM;AACjB;AAEAP,OAAO,CAACI,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}