"""
Advanced Rate Limiting System for Historical Data Loading.
Implements adaptive throttling, circuit breaker patterns, and intelligent backoff strategies.
"""

import time
import logging
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
import random
import math

logger = logging.getLogger(__name__)

class RateLimitStrategy(Enum):
    """Rate limiting strategies."""
    FIXED = "fixed"
    ADAPTIVE = "adaptive"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    TOKEN_BUCKET = "token_bucket"

@dataclass
class RateLimitConfig:
    """Rate limiting configuration."""
    strategy: RateLimitStrategy = RateLimitStrategy.ADAPTIVE
    base_delay: float = 0.1  # Base delay in seconds
    max_delay: float = 60.0  # Maximum delay in seconds
    max_retries: int = 5
    backoff_multiplier: float = 2.0
    jitter_factor: float = 0.1  # Random jitter to prevent thundering herd
    
    # Token bucket parameters
    bucket_size: int = 10
    refill_rate: float = 1.0  # tokens per second
    
    # Adaptive parameters
    success_threshold: float = 0.9  # Success rate threshold for adaptation
    adaptation_window: int = 100  # Number of requests to consider for adaptation
    
    # Circuit breaker parameters
    failure_threshold: int = 10  # Consecutive failures before circuit opens
    recovery_timeout: float = 300.0  # Time to wait before attempting recovery

@dataclass
class RequestStats:
    """Statistics for rate limiting decisions."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    consecutive_failures: int = 0
    last_success_time: Optional[datetime] = None
    last_failure_time: Optional[datetime] = None
    recent_response_times: list = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_response_time(self) -> float:
        """Calculate average response time."""
        if not self.recent_response_times:
            return 0.0
        return sum(self.recent_response_times) / len(self.recent_response_times)

class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"    # Normal operation
    OPEN = "open"        # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing recovery

class AdvancedRateLimiter:
    """Advanced rate limiter with multiple strategies and circuit breaker."""
    
    def __init__(self, config: RateLimitConfig):
        """Initialize the rate limiter."""
        self.config = config
        self.stats = RequestStats()
        self.circuit_state = CircuitState.CLOSED
        self.circuit_opened_time: Optional[datetime] = None
        
        # Token bucket state
        self.tokens = config.bucket_size
        self.last_refill_time = time.time()
        
        # Adaptive delay state
        self.current_delay = config.base_delay
        
        # Thread safety
        self._lock = threading.Lock()
        
        logger.info(f"Initialized rate limiter with strategy: {config.strategy.value}")
    
    def wait_if_needed(self, operation_name: str = "request") -> bool:
        """
        Wait if rate limiting is needed.
        
        Args:
            operation_name: Name of the operation for logging
            
        Returns:
            True if operation should proceed, False if circuit is open
        """
        with self._lock:
            # Check circuit breaker
            if not self._check_circuit_breaker():
                logger.warning(f"Circuit breaker is open, rejecting {operation_name}")
                return False
            
            # Apply rate limiting strategy
            delay = self._calculate_delay()
            
            if delay > 0:
                logger.debug(f"Rate limiting {operation_name}: waiting {delay:.2f}s")
                time.sleep(delay)
            
            return True
    
    def record_success(self, response_time: float = 0.0):
        """Record a successful request."""
        with self._lock:
            self.stats.total_requests += 1
            self.stats.successful_requests += 1
            self.stats.consecutive_failures = 0
            self.stats.last_success_time = datetime.now()
            
            # Track response times for adaptive behavior
            self.stats.recent_response_times.append(response_time)
            if len(self.stats.recent_response_times) > self.config.adaptation_window:
                self.stats.recent_response_times.pop(0)
            
            # Update circuit breaker
            if self.circuit_state == CircuitState.HALF_OPEN:
                self.circuit_state = CircuitState.CLOSED
                logger.info("Circuit breaker closed - recovery successful")
            
            # Adapt delay based on success
            self._adapt_delay(success=True)
    
    def record_failure(self, error_type: str = "unknown"):
        """Record a failed request."""
        with self._lock:
            self.stats.total_requests += 1
            self.stats.failed_requests += 1
            self.stats.consecutive_failures += 1
            self.stats.last_failure_time = datetime.now()
            
            logger.warning(f"Request failed: {error_type} (consecutive failures: {self.stats.consecutive_failures})")
            
            # Update circuit breaker
            if self.stats.consecutive_failures >= self.config.failure_threshold:
                self.circuit_state = CircuitState.OPEN
                self.circuit_opened_time = datetime.now()
                logger.error(f"Circuit breaker opened after {self.stats.consecutive_failures} consecutive failures")
            
            # Adapt delay based on failure
            self._adapt_delay(success=False)
    
    def _check_circuit_breaker(self) -> bool:
        """Check circuit breaker state and handle transitions."""
        if self.circuit_state == CircuitState.CLOSED:
            return True
        
        if self.circuit_state == CircuitState.OPEN:
            if self.circuit_opened_time:
                time_since_opened = datetime.now() - self.circuit_opened_time
                if time_since_opened.total_seconds() >= self.config.recovery_timeout:
                    self.circuit_state = CircuitState.HALF_OPEN
                    logger.info("Circuit breaker transitioning to half-open for testing")
                    return True
            return False
        
        # HALF_OPEN state - allow one request to test
        return True
    
    def _calculate_delay(self) -> float:
        """Calculate delay based on the configured strategy."""
        if self.config.strategy == RateLimitStrategy.FIXED:
            return self._fixed_delay()
        elif self.config.strategy == RateLimitStrategy.ADAPTIVE:
            return self._adaptive_delay()
        elif self.config.strategy == RateLimitStrategy.EXPONENTIAL_BACKOFF:
            return self._exponential_backoff_delay()
        elif self.config.strategy == RateLimitStrategy.TOKEN_BUCKET:
            return self._token_bucket_delay()
        else:
            return self.config.base_delay
    
    def _fixed_delay(self) -> float:
        """Fixed delay strategy."""
        return self.config.base_delay + self._add_jitter(self.config.base_delay)
    
    def _adaptive_delay(self) -> float:
        """Adaptive delay based on success rate and response times."""
        base_delay = self.current_delay
        
        # Add jitter
        jitter = self._add_jitter(base_delay)
        
        return min(base_delay + jitter, self.config.max_delay)
    
    def _exponential_backoff_delay(self) -> float:
        """Exponential backoff based on consecutive failures."""
        if self.stats.consecutive_failures == 0:
            delay = self.config.base_delay
        else:
            delay = self.config.base_delay * (self.config.backoff_multiplier ** self.stats.consecutive_failures)
        
        delay = min(delay, self.config.max_delay)
        return delay + self._add_jitter(delay)
    
    def _token_bucket_delay(self) -> float:
        """Token bucket algorithm."""
        current_time = time.time()
        
        # Refill tokens
        time_passed = current_time - self.last_refill_time
        tokens_to_add = time_passed * self.config.refill_rate
        self.tokens = min(self.config.bucket_size, self.tokens + tokens_to_add)
        self.last_refill_time = current_time
        
        # Check if we have tokens
        if self.tokens >= 1:
            self.tokens -= 1
            return 0.0
        else:
            # Calculate wait time for next token
            wait_time = (1 - self.tokens) / self.config.refill_rate
            return wait_time + self._add_jitter(wait_time)
    
    def _adapt_delay(self, success: bool):
        """Adapt delay based on recent performance."""
        if self.config.strategy != RateLimitStrategy.ADAPTIVE:
            return
        
        # Only adapt if we have enough data
        if self.stats.total_requests < 10:
            return
        
        success_rate = self.stats.success_rate
        avg_response_time = self.stats.average_response_time
        
        if success:
            # Successful request - potentially decrease delay
            if success_rate > self.config.success_threshold and avg_response_time < 2.0:
                # Performance is good, decrease delay slightly
                self.current_delay = max(
                    self.config.base_delay,
                    self.current_delay * 0.95
                )
        else:
            # Failed request - increase delay
            self.current_delay = min(
                self.config.max_delay,
                self.current_delay * 1.5
            )
        
        logger.debug(f"Adapted delay to {self.current_delay:.3f}s (success_rate: {success_rate:.2f})")
    
    def _add_jitter(self, base_value: float) -> float:
        """Add random jitter to prevent thundering herd."""
        jitter_amount = base_value * self.config.jitter_factor
        return random.uniform(-jitter_amount, jitter_amount)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        with self._lock:
            return {
                "total_requests": self.stats.total_requests,
                "successful_requests": self.stats.successful_requests,
                "failed_requests": self.stats.failed_requests,
                "success_rate": self.stats.success_rate,
                "consecutive_failures": self.stats.consecutive_failures,
                "current_delay": self.current_delay,
                "circuit_state": self.circuit_state.value,
                "average_response_time": self.stats.average_response_time,
                "tokens_available": self.tokens if self.config.strategy == RateLimitStrategy.TOKEN_BUCKET else None
            }
    
    def reset_stats(self):
        """Reset statistics."""
        with self._lock:
            self.stats = RequestStats()
            self.circuit_state = CircuitState.CLOSED
            self.circuit_opened_time = None
            self.current_delay = self.config.base_delay
            logger.info("Rate limiter statistics reset")

class RateLimitedExecutor:
    """Executor that applies rate limiting to function calls."""
    
    def __init__(self, rate_limiter: AdvancedRateLimiter):
        """Initialize with a rate limiter."""
        self.rate_limiter = rate_limiter
    
    def execute(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function with rate limiting.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: If circuit breaker is open or function fails
        """
        operation_name = getattr(func, '__name__', 'unknown_operation')
        
        # Check if we should proceed
        if not self.rate_limiter.wait_if_needed(operation_name):
            raise Exception("Circuit breaker is open - operation rejected")
        
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            response_time = time.time() - start_time
            self.rate_limiter.record_success(response_time)
            return result
            
        except Exception as e:
            error_type = type(e).__name__
            self.rate_limiter.record_failure(error_type)
            raise
