{"ast": null, "code": "import count from \"../count.js\";\nexport default function thresholdSturges(values) {\n  return Math.max(1, Math.ceil(Math.log(count(values)) / Math.LN2) + 1);\n}", "map": {"version": 3, "names": ["count", "thresholdSturges", "values", "Math", "max", "ceil", "log", "LN2"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/d3-array/src/threshold/sturges.js"], "sourcesContent": ["import count from \"../count.js\";\n\nexport default function thresholdSturges(values) {\n  return Math.max(1, Math.ceil(Math.log(count(values)) / Math.LN2) + 1);\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAE/B,eAAe,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC/C,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAACN,KAAK,CAACE,MAAM,CAAC,CAAC,GAAGC,IAAI,CAACI,GAAG,CAAC,GAAG,CAAC,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}