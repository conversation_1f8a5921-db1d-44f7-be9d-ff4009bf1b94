# Historical Data Loading System Documentation

## Overview

This document describes the comprehensive historical data loading system for the Signal Stack Trading Platform. The system is designed to fetch historical market data from Fyers API and store it efficiently in TimescaleDB with proper rate limiting, validation, and error handling.

## Architecture

The historical data loading system consists of several interconnected components:

1. **Symbol Mapping Service** - Maps between Fyers API symbols and database symbols
2. **Enhanced Fyers Client** - Handles API communication with rate limiting
3. **Historical Data Loader** - Orchestrates the data loading process
4. **Database Storage Layer** - Manages TimescaleDB storage operations
5. **Validation and Progress Tracking** - Ensures data quality and provides feedback

## Files Involved

### Core Services

#### 1. Symbol Mapping Service
- **File**: `app/services/symbol_mapping_service.py`
- **Purpose**: Downloads and parses Fyers CSV files to create symbol mappings
- **Key Features**:
  - Downloads CSV files from Fyers API URLs (configured in config.yaml)
  - Caches downloaded files for 24 hours
  - Maps NIFTY 50 constituents and indices
  - Handles symbol format conversion (NSE:NIFTY50-INDEX → NIFTY)

#### 2. Enhanced Fyers Client
- **File**: `app/integrations/fyers/fyers_client.py`
- **Purpose**: Handles Fyers API communication with enhanced rate limiting
- **Key Enhancements**:
  - Improved rate limiting for historical data requests
  - Exponential backoff with jitter for retry logic
  - Bulk historical data fetching capabilities
  - Better error handling for different API response codes

#### 3. Historical Data Loader
- **File**: `app/services/historical_data_loader.py`
- **Purpose**: Orchestrates the complete data loading process
- **Key Features**:
  - Progress tracking with callbacks
  - Data validation and error reporting
  - Batch processing for database operations
  - Comprehensive result reporting

#### 4. Enhanced OHLCV Repository
- **File**: `app/database/repositories/ohlcv_repository.py`
- **Purpose**: Manages TimescaleDB operations for OHLCV data
- **Key Enhancements**:
  - Conflict handling for duplicate records
  - Bulk insert operations with error handling
  - Data gap detection methods
  - Record counting and validation methods

### Database Models

#### 5. Database Models
- **File**: `app/database/models.py`
- **Purpose**: Defines SQLAlchemy models for TimescaleDB
- **Key Models**:
  - `StockOHLCV` - 1-minute OHLCV data (TimescaleDB hypertable)
  - `StockOHLCVAgg` - Aggregated timeframe data
  - `Symbol` - Symbol master data

#### 6. Database Initialization
- **File**: `app/database/init_db.py`
- **Purpose**: Sets up TimescaleDB hypertables and indexes
- **Key Features**:
  - Creates TimescaleDB hypertables with appropriate chunk intervals
  - Sets up performance indexes
  - Creates stored procedures for data aggregation

### Scripts and Tools

#### 7. Main Loading Script
- **File**: `scripts/load_nifty_historical_data.py`
- **Purpose**: Command-line script for loading NIFTY historical data
- **Usage**:
  ```bash
  python scripts/load_nifty_historical_data.py [--force] [--validate-only]
  ```
- **Features**:
  - Interactive confirmation for data overwrite
  - Progress tracking and reporting
  - Data validation after loading
  - Comprehensive error reporting

#### 8. Legacy Setup Script (Updated)
- **File**: `scripts/setup_nifty_historical_3months.py`
- **Purpose**: Legacy script updated to work with new system
- **Status**: Updated to use new services but main script is recommended

### Configuration

#### 9. Configuration File
- **File**: `config.yaml`
- **Purpose**: Central configuration for the system
- **Key Settings**:
  ```yaml
  general:
    fyers_api_url: ['https://public.fyers.in/sym_details/NSE_CM.csv']
  
  timeframe:
    interval: 1        # 1 minute for historical data
    days_to_fetch: 90  # 3 months
  
  rate_limit:
    min_delay_seconds: 0.1
    max_retries: 5
    retry_backoff: 3.0
  ```

#### 10. Market Data Service (Updated)
- **File**: `app/services/market_data_service.py`
- **Purpose**: High-level market data operations
- **Updates**: Fixed method signature mismatch for historical data fetching

## Data Flow

### 1. Symbol Setup
```
Symbol Mapping Service → Download CSV → Parse Symbols → Create DB Symbol
```

### 2. Data Fetching
```
Fyers Client → Rate Limited API Calls → OHLCV Data → Validation
```

### 3. Data Storage
```
Validated Data → Batch Processing → TimescaleDB → Conflict Handling
```

### 4. Progress Tracking
```
Loading Progress → Callbacks → User Feedback → Statistics
```

## Usage Instructions

### Prerequisites

1. **Database Setup**:
   ```bash
   python scripts/setup_database.py
   ```

2. **Fyers API Credentials**:
   - Ensure `fyers_client_id.txt` and `fyers_access_token.txt` are configured
   - Or set up `.env` file with Fyers credentials

### Loading NIFTY Historical Data

#### Basic Usage:
```bash
python scripts/load_nifty_historical_data.py
```

#### Force Refresh:
```bash
python scripts/load_nifty_historical_data.py --force
```

#### Validate Existing Data:
```bash
python scripts/load_nifty_historical_data.py --validate-only
```

### Expected Output

The script will:
1. Validate prerequisites (database, Fyers auth, symbol mapping)
2. Check existing data and prompt for confirmation if needed
3. Load 3 months of 1-minute NIFTY data
4. Validate the loaded data for completeness and gaps
5. Provide comprehensive statistics and results

### Sample Output:
```
🚀 NIFTY Historical Data Loading Script
============================================================
Step 1: Initializing services...
✓ Services initialized successfully

Step 2: Validating prerequisites...
   Checking symbol mapping service...
   ✓ NIFTY mapping found: NSE:NIFTY50-INDEX
   Testing Fyers authentication...
   ✓ Fyers authentication successful
✓ All prerequisites validated

Step 3: Loading historical data...
📥 Loading 3 months of 1-minute NIFTY data...
   Received 27,000 records from Fyers
   Valid records: 27,000/27,000
   Records loaded: 26,850
   Records skipped: 150

📈 LOADING RESULTS
============================================================
Symbol: NIFTY
Success: ✅ YES
Records loaded: 26,850
Records skipped: 150
Duration: 45.2 seconds

📊 Data Statistics:
   Total records: 26,850
   Data range: 2024-04-15 09:15:00 to 2024-07-13 15:30:00
   Latest price: ₹24,315.95

📈 Data Completeness:
   Expected records (approx): 33,750
   Actual records: 26,850
   Completeness: 79.5%

✓ No significant data gaps found
✅ Data validation PASSED

🎉 Operation completed successfully!
✓ NIFTY historical data is ready for analysis
```

## Rate Limiting Strategy

The system implements a multi-layered rate limiting approach:

1. **Base Rate Limiting**: Minimum 0.1 seconds between requests
2. **Historical Data Rate Limiting**: Minimum 0.5 seconds for historical requests
3. **Exponential Backoff**: 2^attempt * base_delay for retries
4. **Jitter**: Random component to prevent thundering herd
5. **Circuit Breaker**: Stops after max retries to prevent API blocking

## Error Handling

The system handles various error scenarios:

1. **API Errors**: Rate limiting, invalid symbols, network issues
2. **Data Validation Errors**: Invalid OHLCV values, timestamp issues
3. **Database Errors**: Connection issues, constraint violations
4. **System Errors**: Memory issues, disk space, permissions

## Performance Considerations

1. **Batch Processing**: Database operations are batched for efficiency
2. **Memory Management**: Large datasets are processed in chunks
3. **Connection Pooling**: Database connections are pooled and reused
4. **Caching**: Symbol mappings are cached to reduce API calls
5. **TimescaleDB Optimization**: Uses hypertables for time-series data

## Monitoring and Validation

The system provides comprehensive monitoring:

1. **Progress Tracking**: Real-time progress updates during loading
2. **Data Validation**: Checks for data completeness and gaps
3. **Statistics**: Detailed statistics on loaded data
4. **Error Reporting**: Comprehensive error logging and reporting
5. **Performance Metrics**: Loading speed and efficiency metrics

## Troubleshooting

### Common Issues:

1. **Authentication Failures**: Check Fyers credentials and token validity
2. **Rate Limiting**: Increase delays in config.yaml if hitting limits
3. **Database Connection**: Verify PostgreSQL/TimescaleDB is running
4. **Symbol Mapping**: Check if CSV URLs are accessible
5. **Data Gaps**: Normal for weekends/holidays, validate against trading calendar

### Debug Mode:
Set logging level to DEBUG in the configuration for detailed troubleshooting information.

## 15-Year Data Loading System

### Enhanced Components for Large-Scale Data Loading

#### 1. Advanced Rate Limiter
- **File**: `app/core/rate_limiter.py`
- **Purpose**: Sophisticated rate limiting with multiple strategies
- **Features**:
  - Adaptive throttling based on API response patterns
  - Circuit breaker pattern for fault tolerance
  - Token bucket algorithm for burst handling
  - Exponential backoff with jitter
  - Performance metrics and monitoring

#### 2. Chunked Data Loader
- **File**: `app/services/chunked_data_loader.py`
- **Purpose**: Manages large dataset loading in manageable chunks
- **Features**:
  - Intelligent chunking strategy (30-day chunks by default)
  - Error recovery and retry logic per chunk
  - Progress persistence and resume capability
  - Rate-limited API calls with adaptive throttling
  - Comprehensive error handling and logging

#### 3. Progress Manager
- **File**: `app/services/progress_manager.py`
- **Purpose**: Persistent progress tracking for long-running operations
- **Features**:
  - SQLite-based progress persistence
  - Operation metrics and performance tracking
  - Resume capability after interruption
  - Real-time progress callbacks
  - Cleanup of old operation data

#### 4. 15-Year Loading Script
- **File**: `scripts/load_15year_nifty_data.py`
- **Purpose**: Specialized script for loading 15 years of historical data
- **Features**:
  - Graceful interrupt handling (Ctrl+C to pause)
  - Session management and resume capability
  - Comprehensive progress monitoring
  - Final statistics and validation
  - Command-line interface with options

### Usage for 15-Year Data Loading

#### Basic 15-Year Load:
```bash
python scripts/load_15year_nifty_data.py
```

#### Custom Year Range:
```bash
python scripts/load_15year_nifty_data.py --years 10
```

#### Resume Interrupted Session:
```bash
python scripts/load_15year_nifty_data.py --resume session_abc123def456
```

#### List Available Sessions:
```bash
python scripts/load_15year_nifty_data.py --list-sessions
```

#### Custom Chunk Size:
```bash
python scripts/load_15year_nifty_data.py --chunk-size 15
```

### Expected Performance

For 15 years of 1-minute NIFTY data:
- **Estimated Records**: ~1.4 million records
- **Estimated Duration**: 4-8 hours (depending on API limits)
- **Chunk Strategy**: 30-day chunks (180 chunks total)
- **Rate Limiting**: Adaptive 0.5-30 second delays
- **Resume Capability**: Full session state persistence

### Advanced Rate Limiting Strategies

#### 1. Adaptive Strategy (Default)
- Automatically adjusts delays based on success rate
- Reduces delays when API responds well
- Increases delays when errors occur
- Optimal for long-running operations

#### 2. Token Bucket Strategy
- Allows burst requests when tokens available
- Steady refill rate for sustained throughput
- Good for APIs with burst allowances

#### 3. Exponential Backoff Strategy
- Increases delays exponentially on failures
- Resets on successful requests
- Conservative approach for unstable APIs

#### 4. Circuit Breaker Pattern
- Opens circuit after consecutive failures
- Prevents overwhelming failing APIs
- Automatic recovery testing

### Monitoring and Observability

The system provides comprehensive monitoring:

#### Progress Tracking
- Real-time progress updates
- ETA calculations
- Chunk-level status tracking
- Session persistence across restarts

#### Performance Metrics
- Records processed per second
- API call success rates
- Error rates and retry counts
- Memory and processing statistics

#### Rate Limiter Statistics
- Current delay settings
- Circuit breaker status
- Request success patterns
- Adaptive behavior metrics

### Error Handling and Recovery

#### Chunk-Level Recovery
- Individual chunk failures don't stop entire operation
- Automatic retry with exponential backoff
- Maximum retry limits to prevent infinite loops
- Detailed error logging for troubleshooting

#### Session-Level Recovery
- Full session state persistence
- Resume from exact point of interruption
- Graceful shutdown on interrupt signals
- Progress preservation across system restarts

#### API-Level Recovery
- Circuit breaker prevents API overload
- Adaptive rate limiting responds to API behavior
- Intelligent retry strategies
- Comprehensive error classification

### Database Optimization for Large Datasets

#### TimescaleDB Features
- Automatic partitioning by time
- Compression for historical data
- Optimized indexes for time-series queries
- Efficient bulk insert operations

#### Conflict Handling
- Duplicate record detection and skipping
- Upsert capabilities for data updates
- Batch processing for performance
- Transaction management for consistency

## Future Enhancements

1. **Multi-Symbol Support**: Extend to load multiple symbols simultaneously
2. **Incremental Updates**: Load only missing data ranges
3. **Real-time Integration**: Connect with live data feeds
4. **Data Quality Metrics**: Advanced data quality scoring
5. **Performance Optimization**: Further optimize for large datasets
6. **Distributed Loading**: Scale across multiple workers
7. **Cloud Storage Integration**: Direct loading to cloud databases
8. **Advanced Analytics**: Real-time data quality monitoring
