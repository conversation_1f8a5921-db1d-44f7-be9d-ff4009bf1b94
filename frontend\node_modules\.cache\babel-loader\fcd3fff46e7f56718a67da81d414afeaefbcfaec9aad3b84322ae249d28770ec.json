{"ast": null, "code": "/**\n * Type guard to check if the object has a \"main\" property of type string.\n *\n * @param obj - the object to check\n * @returns boolean\n */\nfunction hasCorrectMainProperty(obj) {\n  return typeof obj.main === 'string';\n}\n/**\n * Checks if the object conforms to the SimplePaletteColorOptions type.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param obj - The object to check\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns boolean\n */\nfunction checkSimplePaletteColorValues(obj, additionalPropertiesToCheck = []) {\n  if (!hasCorrectMainProperty(obj)) {\n    return false;\n  }\n  for (const value of additionalPropertiesToCheck) {\n    if (!obj.hasOwnProperty(value) || typeof obj[value] !== 'string') {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Creates a filter function used to filter simple palette color options.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns ([, value]: [any, PaletteColorOptions]) => boolean\n */\nexport default function createSimplePaletteValueFilter(additionalPropertiesToCheck = []) {\n  return ([, value]) => value && checkSimplePaletteColorValues(value, additionalPropertiesToCheck);\n}", "map": {"version": 3, "names": ["hasCorrectMainProperty", "obj", "main", "checkSimplePaletteColorValues", "additionalPropertiesToCheck", "value", "hasOwnProperty", "createSimplePaletteValueFilter"], "sources": ["C:/Users/<USER>/Desktop/Python/signal_stack/frontend/node_modules/@mui/material/esm/utils/createSimplePaletteValueFilter.js"], "sourcesContent": ["/**\n * Type guard to check if the object has a \"main\" property of type string.\n *\n * @param obj - the object to check\n * @returns boolean\n */\nfunction hasCorrectMainProperty(obj) {\n  return typeof obj.main === 'string';\n}\n/**\n * Checks if the object conforms to the SimplePaletteColorOptions type.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param obj - The object to check\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns boolean\n */\nfunction checkSimplePaletteColorValues(obj, additionalPropertiesToCheck = []) {\n  if (!hasCorrectMainProperty(obj)) {\n    return false;\n  }\n  for (const value of additionalPropertiesToCheck) {\n    if (!obj.hasOwnProperty(value) || typeof obj[value] !== 'string') {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Creates a filter function used to filter simple palette color options.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns ([, value]: [any, PaletteColorOptions]) => boolean\n */\nexport default function createSimplePaletteValueFilter(additionalPropertiesToCheck = []) {\n  return ([, value]) => value && checkSimplePaletteColorValues(value, additionalPropertiesToCheck);\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,sBAAsBA,CAACC,GAAG,EAAE;EACnC,OAAO,OAAOA,GAAG,CAACC,IAAI,KAAK,QAAQ;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACF,GAAG,EAAEG,2BAA2B,GAAG,EAAE,EAAE;EAC5E,IAAI,CAACJ,sBAAsB,CAACC,GAAG,CAAC,EAAE;IAChC,OAAO,KAAK;EACd;EACA,KAAK,MAAMI,KAAK,IAAID,2BAA2B,EAAE;IAC/C,IAAI,CAACH,GAAG,CAACK,cAAc,CAACD,KAAK,CAAC,IAAI,OAAOJ,GAAG,CAACI,KAAK,CAAC,KAAK,QAAQ,EAAE;MAChE,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,8BAA8BA,CAACH,2BAA2B,GAAG,EAAE,EAAE;EACvF,OAAO,CAAC,GAAGC,KAAK,CAAC,KAAKA,KAAK,IAAIF,6BAA6B,CAACE,KAAK,EAAED,2BAA2B,CAAC;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}